#include "ldmanager.h"
#include "ccompatibletype.h"
#include "hlog.h"
#include "ldcoordinatefixer.h"
#include "ldobject.h"
#include <QCoreApplication>
#include <QDebug>
#include <QDir>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonParseError>
#include <QPair>
#include <QQueue>
#include <QtMath>
#include <algorithm>

LDManager &LDManager::instance()
{
    static LDManager instance;
    return instance;
}

void LDManager::init(const QString &appPath)
{
    appDir = appPath;
    // 读取数据类型兼容列表
    // CCompatibleTypeCFC::instance()->readFile(appDir);
}

void LDManager::OpenProject(const QString &pName, const QString &pPath)
{
    projectName = pName;
    projectDir = pPath;

    LDFileList.clear();
    // 读取功能块列表
}

// 关闭当前项目
void LDManager::CloseProject()
{
    projectName.clear();
    projectDir.clear();
    LDFileList.clear();
    // 清除功能块列表
}

QString LDManager::initFile(const QString &fileName, const QString &fileType, const QString &codeType)
{
    QDomDocument doc;
    // 写入xml头部
    QDomProcessingInstruction instruction; // 添加处理命令
    instruction = doc.createProcessingInstruction("xml", "version=\"1.0\" encoding=\"UTF-8\"");
    doc.appendChild(instruction);
    // 添加根节点
    QDomElement root = doc.createElement("LDFile");
    root.setAttribute("Name", fileName);
    root.setAttribute("Code", codeType);
    root.setAttribute("Type", fileType);
    root.setAttribute("Version", "1.0.0");
    root.setAttribute("WidthNumber", "100");
    root.setAttribute("HeightNumber", "100");
    root.setAttribute("Author", QString());
    root.setAttribute("CreatedOn", QDate::currentDate().toString("yyyy/MM/dd"));
    root.setAttribute("LastChange", QDate::currentDate().toString("yyyy/MM/dd"));
    root.setAttribute("Comment", QString());
    root.setAttribute("TasksName", "T2");

    // 表
    QDomElement networks = doc.createElement("LDNetworks");
    QDomElement network = doc.createElement("LDNetwork");
    network.setAttribute("Type", "LDNetwork");
    network.setAttribute("Number", "0");
    network.setAttribute("Enable", "???");
    network.setAttribute("Label", "");
    networks.appendChild(network);
    root.appendChild(networks);

    // LDComponents
    QDomElement components = doc.createElement("LDComponents");

    // 添加默认起始元件
    QDomElement startComponent = doc.createElement("LDComponent");
    startComponent.setAttribute("Type", "Variable");
    startComponent.setAttribute("SupportsOutPinAdd", "0");
    startComponent.setAttribute("Number", "???");
    startComponent.setAttribute("AuxContent", "???");
    startComponent.setAttribute("SupportsInPinType", "");
    startComponent.setAttribute("Name", "Variable_Start");
    startComponent.setAttribute("InstanceName", "BOOL_Start");
    startComponent.setAttribute("ParentNumber", "-1");
    startComponent.setAttribute("Source", "");
    startComponent.setAttribute("Enable", "???");
    startComponent.setAttribute("SupportsOutPinType", "");
    startComponent.setAttribute("YPos", "0");
    startComponent.setAttribute("XPos", "0");
    startComponent.setAttribute("Visible", "0");
    startComponent.setAttribute("AuxContent1", "Start");
    startComponent.setAttribute("LeftOrRight", "3");
    startComponent.setAttribute("DataType_Local", "");
    startComponent.setAttribute("ParentPinId", "0");
    startComponent.setAttribute("ChildType", "Constant");
    startComponent.setAttribute("Width", "3");
    startComponent.setAttribute("NetworkNumber", "0");
    startComponent.setAttribute("Comment", "");
    startComponent.setAttribute("SupportsInPinAdd", "0");
    startComponent.setAttribute("Height", "3");
    startComponent.setAttribute("MinWidth", "3");
    startComponent.setAttribute("ParentPinDataType", "");
    startComponent.setAttribute("TaskName", "");
    startComponent.setAttribute("TaskOrderNumber", "0");

    // 添加EN连接器
    QDomElement enConnector = doc.createElement("LDConnector");
    enConnector.setAttribute("Direction", "EN");
    enConnector.setAttribute("DataType", "Variable");
    enConnector.setAttribute("NewAdd", "0");
    enConnector.setAttribute("InitValue", "");
    enConnector.setAttribute("SupportChangeDataType", "0");
    enConnector.setAttribute("Name", "EN");
    enConnector.setAttribute("isInitVar", "0");
    enConnector.setAttribute("XPos", "0");
    enConnector.setAttribute("Comment", "");
    enConnector.setAttribute("IsLogical", "0");
    enConnector.setAttribute("ChildNumber", "-1");
    enConnector.setAttribute("YPos", "0");
    enConnector.setAttribute("DataTypeGroup", "");
    enConnector.setAttribute("Negated", "0");
    enConnector.setAttribute("FastSignal", "0");
    enConnector.setAttribute("InstanceName", "EN_a4823c2ac3da46e586ad0f84f68a5402");
    enConnector.setAttribute("PinId", "???");
    enConnector.setAttribute("Visible", "???");
    startComponent.appendChild(enConnector);

    // 添加ENO连接器
    QDomElement enoConnector = doc.createElement("LDConnector");
    enoConnector.setAttribute("Direction", "ENO");
    enoConnector.setAttribute("DataType", "Variable");
    enoConnector.setAttribute("NewAdd", "0");
    enoConnector.setAttribute("InitValue", "");
    enoConnector.setAttribute("SupportChangeDataType", "0");
    enoConnector.setAttribute("Name", "ENO");
    enoConnector.setAttribute("isInitVar", "0");
    enoConnector.setAttribute("XPos", "0");
    enoConnector.setAttribute("Comment", "");
    enoConnector.setAttribute("IsLogical", "0");
    enoConnector.setAttribute("ChildNumber", "-1");
    enoConnector.setAttribute("YPos", "0");
    enoConnector.setAttribute("DataTypeGroup", "");
    enoConnector.setAttribute("Negated", "0");
    enoConnector.setAttribute("FastSignal", "0");
    enoConnector.setAttribute("InstanceName", "");
    enoConnector.setAttribute("PinId", "-1");
    enoConnector.setAttribute("Visible", "???");
    startComponent.appendChild(enoConnector);

    // 添加OUT1连接器
    QDomElement outConnector = doc.createElement("LDConnector");
    outConnector.setAttribute("Direction", "Output");
    outConnector.setAttribute("DataType", "Variable");
    outConnector.setAttribute("NewAdd", "0");
    outConnector.setAttribute("InitValue", "");
    outConnector.setAttribute("SupportChangeDataType", "0");
    outConnector.setAttribute("Name", "OUT1");
    outConnector.setAttribute("isInitVar", "0");
    outConnector.setAttribute("XPos", "0");
    outConnector.setAttribute("Comment", "");
    outConnector.setAttribute("IsLogical", "0");
    outConnector.setAttribute("ChildNumber", "-1");
    outConnector.setAttribute("YPos", "0");
    outConnector.setAttribute("DataTypeGroup", "");
    outConnector.setAttribute("Negated", "0");
    outConnector.setAttribute("FastSignal", "0");
    outConnector.setAttribute("InstanceName", "");
    outConnector.setAttribute("PinId", "-2");
    outConnector.setAttribute("Visible", "0");
    startComponent.appendChild(outConnector);

    components.appendChild(startComponent);

    root.appendChild(components);

    // LDConnections
    QDomElement connections = doc.createElement("LDConnections");
    root.appendChild(connections);

    // 接口
    QDomElement interface = doc.createElement("Interface");
    QDomElement inputv = doc.createElement("InputVariables");
    QDomElement outputv = doc.createElement("OutputVariables");
    interface.appendChild(inputv);
    interface.appendChild(outputv);
    root.appendChild(interface);

    doc.appendChild(root);
    // 将doc转化为字符串
    return doc.toString();
}

bool LDManager::readFile(const QString &deviceName, const QString &fileType, const QString &fileName,
                         const QString &filePath)
{
    if (projectDir.isEmpty())
    {
        qDebug() << "newld editor projectDir.isEmpty";
        return false;
    }
    else
    {
        // 根据项目路径和fileType
        qDebug() << "ReadFile LD filePath" << filePath;
        // 判断文件是否存在
        QFileInfo fileInfo(filePath);
        if (!fileInfo.isFile())
        {
            qDebug() << "filePath is emplty";
            return false;
        }
        else
        {
            QSharedPointer<LDFile> newFile = QSharedPointer<LDFile>(new LDFile());
            newFile->fromXml(filePath);
            newFile->filePath = filePath;
            newFile->compliePath = filePath;
            newFile->compliePath = newFile->compliePath.replace("/User/", "/" + deviceName + "/");
            qDebug() << "ReadFile check graphic file Read Ok:" << newFile->Name << newFile->compliePath;
            LDFileList.insert(filePath, newFile);
            qDebug() << "FileList count:" << LDFileList.count();
        }
    }
    return true;
}

// 读取加密文件
bool LDManager::readFile(const QString &deviceName, const QString &fileType, const QString &fileName,
                         const QString &filePath, const QByteArray &bytes)
{
    QSharedPointer<LDFile> newFile = QSharedPointer<LDFile>(new LDFile());
    newFile->fromXml(bytes);
    newFile->filePath = filePath;
    newFile->compliePath = filePath;
    newFile->compliePath = newFile->compliePath.replace("/User/", "/" + deviceName + "/");
    qDebug() << "ReadFile check graphic file Read Ok:" << newFile->Name << newFile->compliePath;
    LDFileList.insert(filePath, newFile);
    qDebug() << "FileList count:" << LDFileList.count();

    return true;
}

LDFile *LDManager::getFile(QString path)
{
    LDFile *newFile = new LDFile();
    QFileInfo fileInfo(path);
    if (fileInfo.isFile())
    {
        newFile->fromXml(path);
        newFile->filePath = path;
    }
    return newFile;
}

QByteArray LDManager::getFileData(const QString &fileKey)
{
    QByteArray bytes;
    if (LDFileList.contains(fileKey))
    {
        LDFile *ld = LDFileList[fileKey].data();
        ld->LastChange = QDate::currentDate().toString("yyyy/MM/dd");
        bytes = ld->toXml();
    }
    return bytes;
}

bool LDManager::saveFile(const QString &fileKey)
{
    if (LDFileList.contains(fileKey))
    {
        LDFile *ld = LDFileList[fileKey].data();
        for (auto &network : ld->networks->networkMap)
        {
            if (!fixBlockConnection(ld, network->Number))
            {
                LOG_ERROR_DEFAULT("saveFile fixBlockConnection failed");
                return false;
            }
        }
        ld->LastChange = QDate::currentDate().toString("yyyy/MM/dd");
        ld->toXml(fileKey);
        return true;
    }
    return false;
}

// 保存文件
bool LDManager::saveFile(const QString &fileKey, QJsonArray data)
{
    if (LDFileList.contains(fileKey))
    {
        LDFile *ld = LDFileList[fileKey].data();

        // 清空现有数据
        ld->networks->networkMap.clear();
        ld->components->componentMap.clear();
        ld->connections->connectionList.clear();

        // 遍历JSON数据中的每个网络
        for (int i = 0; i < data.size(); i++)
        {
            QJsonObject networkObj = data[i].toObject();

            // 创建一个新的网络对象
            QSharedPointer<LDNetwork> network = QSharedPointer<LDNetwork>(new LDNetwork());

            // 填充网络基本属性
            network->Number = networkObj["Number"].toInt();
            network->Type = networkObj["Type"].toString();
            network->Label = networkObj["Label"].toString();
            network->Enable = networkObj["Enable"].toBool();
            network->Comment = networkObj["Comment"].toString();
            network->Height = networkObj["Height"].toInt(30); // 默认高度为30

            // 添加网络到LDFile
            ld->networks->networkMap.insert(network->Number, network);

            // 处理元件
            if (networkObj.contains("components"))
            {
                QJsonArray componentsArray = networkObj["components"].toArray();
                for (int j = 0; j < componentsArray.size(); j++)
                {
                    QJsonObject componentObj = componentsArray[j].toObject();

                    // 创建元件
                    QSharedPointer<LDComponent> component = QSharedPointer<LDComponent>(new LDComponent());

                    // 设置元件属性
                    component->Name = componentObj["Name"].toString();
                    component->InstanceName = componentObj["InstanceName"].toString();
                    component->NetworkNumber = componentObj["NetworkNumber"].toInt();
                    component->Enable = componentObj["Enable"].toBool();
                    component->Number = componentObj["Number"].toInt();
                    component->TaskName = componentObj["TaskName"].toString();
                    component->TaskOrderNumber = componentObj["TaskOrderNumber"].toInt();
                    component->Type = componentObj["Type"].toString();
                    component->ChildType = componentObj["ChildType"].toString();
                    component->AuxContent = componentObj["AuxContent"].toString();
                    component->AuxContent1 = componentObj["AuxContent1"].toString();
                    component->DataType_Local = componentObj["DataType_Local"].toString();
                    component->Source = componentObj["Source"].toString();
                    component->InputPinNum = componentObj["InputPinNum"].toInt();
                    component->OutputPinNum = componentObj["OutputPinNum"].toInt();
                    component->Comment = componentObj["Comment"].toString();
                    component->SupportsInPinType = componentObj["SupportsInPinType"].toString();
                    component->SupportsOutPinType = componentObj["SupportsOutPinType"].toString();
                    component->SupportsInPinAdd = componentObj["SupportsInPinAdd"].toBool();
                    component->SupportsOutPinAdd = componentObj["SupportsOutPinAdd"].toBool();
                    component->XPos = componentObj["XPos"].toInt();
                    component->YPos = componentObj["YPos"].toInt();
                    component->Width = componentObj["Width"].toInt();
                    component->Height = componentObj["Height"].toInt();
                    component->MinWidth = componentObj["MinWidth"].toInt(3); // 默认最小宽度为3
                    component->Visible = componentObj["Visible"].toBool();
                    component->LeftOrRight = componentObj["LeftOrRight"].toInt();
                    component->ParentNumber = componentObj["ParentNumber"].toInt();
                    component->ParentPinId = componentObj["ParentPinId"].toInt();
                    component->ParentPinDataType = componentObj["ParentPinDataType"].toString();

                    // 处理引脚
                    if (componentObj.contains("connectors"))
                    {
                        QJsonArray connectorsArray = componentObj["connectors"].toArray();
                        for (int k = 0; k < connectorsArray.size(); k++)
                        {
                            QJsonObject connectorObj = connectorsArray[k].toObject();

                            // 创建引脚
                            QSharedPointer<LDConnector> connector = QSharedPointer<LDConnector>(new LDConnector());

                            // 设置引脚属性
                            connector->InstanceName = connectorObj["InstanceName"].toString();
                            connector->PinId = connectorObj["PinId"].toInt();
                            connector->NewAdd = connectorObj["NewAdd"].toBool();
                            connector->Name = connectorObj["Name"].toString();
                            connector->DataType = connectorObj["DataType"].toString();
                            connector->SupportChangeDataType = connectorObj["SupportChangeDataType"].toBool();
                            connector->DataTypeGroup = connectorObj["DataTypeGroup"].toString();
                            connector->Negated = connectorObj["Negated"].toBool();
                            connector->Direction = connectorObj["Direction"].toString();
                            connector->Comment = connectorObj["Comment"].toString();
                            connector->FastSignal = connectorObj["FastSignal"].toBool();
                            connector->InitValue = connectorObj["InitValue"].toString();
                            connector->isInitVar = connectorObj["isInitVar"].toBool();
                            connector->XPos = connectorObj["XPos"].toInt();
                            connector->YPos = connectorObj["YPos"].toInt();
                            connector->Visible = connectorObj["Visible"].toBool();
                            connector->IsLogical = connectorObj["IsLogical"].toBool();
                            connector->ChildNumber = connectorObj["ChildNumber"].toInt();

                            // 添加引脚到元件
                            component->connectorList.append(connector);
                        }
                    }

                    // 添加元件到映射
                    component->network = network;
                    ld->components->componentMap.insert(component->Number, component);
                    network->componentMap.insert(component->Number, component);
                }
            }

            // 处理连接
            if (networkObj.contains("connections"))
            {
                QJsonArray connectionsArray = networkObj["connections"].toArray();
                for (int j = 0; j < connectionsArray.size(); j++)
                {
                    QJsonObject connectionObj = connectionsArray[j].toObject();

                    // 创建连接
                    QSharedPointer<LDConnection> connection = QSharedPointer<LDConnection>(new LDConnection());

                    // 设置连接属性
                    connection->InstanceName = connectionObj["InstanceName"].toString();
                    connection->SourceDataType = connectionObj["SourceDataType"].toString();
                    connection->SourceComponentNumber = connectionObj["SourceComponentNumber"].toInt();
                    connection->SourcePinId = connectionObj["SourcePinId"].toInt();
                    connection->TargetDataType = connectionObj["TargetDataType"].toString();
                    connection->TargetComponentNumber = connectionObj["TargetComponentNumber"].toInt();
                    connection->TargetPinId = connectionObj["TargetPinId"].toInt();
                    connection->PlotPath = connectionObj["PlotPath"].toString();
                    connection->Length = connectionObj["Length"].toInt();
                    connection->Visible = connectionObj["Visible"].toBool(true);
                    connection->VarName = connectionObj["VarName"].toString();
                    connection->VarOwned = connectionObj["VarOwned"].toString();
                    connection->TaskName = connectionObj["TaskName"].toString();
                    connection->TaskOrderNumber = connectionObj["TaskOrderNumber"].toInt(9999);
                    connection->SourceConnectIndex = connectionObj["SourceConnectIndex"].toInt();
                    connection->TargetConnectIndex = connectionObj["TargetConnectIndex"].toInt();

                    // 添加连接到列表
                    ld->connections->connectionList.append(connection);
                }
            }

            // 处理变量块
            if (networkObj.contains("variableBlocks"))
            {
                QJsonArray variableBlocksArray = networkObj["variableBlocks"].toArray();
                for (int j = 0; j < variableBlocksArray.size(); j++)
                {
                    QJsonObject blockObj = variableBlocksArray[j].toObject();

                    // 创建元件
                    QSharedPointer<LDComponent> block = QSharedPointer<LDComponent>(new LDComponent());

                    // 设置元件属性
                    block->Name = blockObj["Name"].toString();
                    block->InstanceName = blockObj["InstanceName"].toString();
                    block->NetworkNumber = blockObj["NetworkNumber"].toInt();
                    block->Enable = blockObj["Enable"].toBool();
                    block->Number = blockObj["Number"].toInt();
                    block->TaskName = blockObj["TaskName"].toString();
                    block->TaskOrderNumber = blockObj["TaskOrderNumber"].toInt();
                    block->Type = blockObj["Type"].toString();
                    block->ChildType = blockObj["ChildType"].toString();
                    block->AuxContent = blockObj["AuxContent"].toString();
                    block->AuxContent1 = blockObj["AuxContent1"].toString();
                    block->DataType_Local = blockObj["DataType_Local"].toString();
                    block->Source = blockObj["Source"].toString();
                    block->InputPinNum = blockObj["InputPinNum"].toInt();
                    block->OutputPinNum = blockObj["OutputPinNum"].toInt();
                    block->Comment = blockObj["Comment"].toString();
                    block->SupportsInPinType = blockObj["SupportsInPinType"].toString();
                    block->SupportsOutPinType = blockObj["SupportsOutPinType"].toString();
                    block->SupportsInPinAdd = blockObj["SupportsInPinAdd"].toBool();
                    block->SupportsOutPinAdd = blockObj["SupportsOutPinAdd"].toBool();
                    block->XPos = blockObj["XPos"].toInt();
                    block->YPos = blockObj["YPos"].toInt();
                    block->Width = blockObj["Width"].toInt();
                    block->Height = blockObj["Height"].toInt();
                    block->MinWidth = blockObj["MinWidth"].toInt(3); // 默认最小宽度为3
                    block->Visible = blockObj["Visible"].toBool();
                    block->LeftOrRight = blockObj["LeftOrRight"].toInt();
                    block->ParentNumber = blockObj["ParentNumber"].toInt();
                    block->ParentPinId = blockObj["ParentPinId"].toInt();
                    block->ParentPinDataType = blockObj["ParentPinDataType"].toString();

                    // 处理引脚
                    if (blockObj.contains("connectors"))
                    {
                        QJsonArray blockConnectorsArray = blockObj["connectors"].toArray();
                        for (int k = 0; k < blockConnectorsArray.size(); k++)
                        {
                            QJsonObject connectorObj = blockConnectorsArray[k].toObject();

                            // 创建引脚
                            QSharedPointer<LDConnector> connector = QSharedPointer<LDConnector>(new LDConnector());

                            // 设置引脚属性
                            connector->InstanceName = connectorObj["InstanceName"].toString();
                            connector->PinId = connectorObj["PinId"].toInt();
                            connector->NewAdd = connectorObj["NewAdd"].toBool();
                            connector->Name = connectorObj["Name"].toString();
                            connector->DataType = connectorObj["DataType"].toString();
                            connector->SupportChangeDataType = connectorObj["SupportChangeDataType"].toBool();
                            connector->DataTypeGroup = connectorObj["DataTypeGroup"].toString();
                            connector->Negated = connectorObj["Negated"].toBool();
                            connector->Direction = connectorObj["Direction"].toString();
                            connector->Comment = connectorObj["Comment"].toString();
                            connector->FastSignal = connectorObj["FastSignal"].toBool();
                            connector->InitValue = connectorObj["InitValue"].toString();
                            connector->isInitVar = connectorObj["isInitVar"].toBool();
                            connector->XPos = connectorObj["XPos"].toInt();
                            connector->YPos = connectorObj["YPos"].toInt();
                            connector->Visible = connectorObj["Visible"].toBool();
                            connector->IsLogical = connectorObj["IsLogical"].toBool();
                            connector->ChildNumber = connectorObj["ChildNumber"].toInt();

                            // 添加引脚到变量块
                            block->connectorList.append(connector);
                        }
                    }

                    // 添加变量块到映射
                    // 查找所属的网络
                    QSharedPointer<LDNetwork> blockNetwork = ld->networks->networkMap.value(block->NetworkNumber);
                    if (blockNetwork)
                    {
                        block->network = blockNetwork;
                        ld->components->componentMap.insert(block->Number, block);
                        blockNetwork->componentMap.insert(block->Number, block);
                    }
                }
            }
        }

        // 重建网络与元件的关系
        ld->networkWithComponent();

        // 更新修改时间
        ld->LastChange = QDate::currentDate().toString("yyyy/MM/dd");

        for (auto &network : ld->networks->networkMap)
        {
            if (!restoreBlockConnection(ld, network->Number))
            {
                return false;
            }
        }
        return true;
    }
    return false;
}

void LDManager::saveAllFile()
{
    QMap<QString, QSharedPointer<LDFile>>::Iterator iter;
    for (iter = LDFileList.begin(); iter != LDFileList.end(); ++iter)
    {
        LDFile *ld = iter.value().data();
        ld->LastChange = QDate::currentDate().toString("yyyy/MM/dd");
        ld->toXml(ld->filePath);
    }
}

QJsonObject LDManager::getPlanInfo(const QString &fileKey)
{
    QJsonObject obj;
    if (LDFileList.contains(fileKey))
    {
        LDFile *ld = LDFileList[fileKey].data();
        obj = ld->toJsonObject();
    }
    return obj;
}

void LDManager::modifyTaskName(const QString &fileKey, const QString taskName)
{
    qDebug() << "modifyTaskName" << fileKey << taskName;
    // 已在内存中
    if (LDFileList.contains(fileKey))
    {
        LDFile *ld = LDFileList[fileKey].data();
        if (ld->Code != "CFC")
        {
            for (auto &component : ld->components->componentMap)
            {
                if (component->Type == "Block")
                {
                    component->TaskName = taskName;
                }
            }
        }
        // 保存文件
        ld->TasksName = taskName;
        ld->LastChange = QDate::currentDate().toString("yyyy/MM/dd");
        ld->toXml(fileKey);
    }
    else
    {
        // 读文件到内存中然后再修改保存
        QSharedPointer<LDFile> newFile = QSharedPointer<LDFile>(new LDFile());
        newFile->fromXml(fileKey);
        newFile->filePath = fileKey;
        newFile->compliePath = fileKey;
        if (newFile->Code != "CFC")
        {
            for (auto &component : newFile->components->componentMap)
            {
                if (component->Type == "Block")
                {
                    component->TaskName = taskName;
                }
            }
        }
        // 保存文件
        newFile->TasksName = taskName;
        newFile->LastChange = QDate::currentDate().toString("yyyy/MM/dd");
        newFile->toXml(fileKey);
    }
}

void LDManager::updateAuthorComment(const QString &fileKey, const QString &author, const QString &comment)
{
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        ld->Author = author;
        ld->Comment = comment;
        // 更新文件吗？
        // cfcFile->WriteFile();
    }
}

QJsonArray LDManager::getNetwork(const QString &fileKey)
{
    QJsonArray ary;
    if (LDFileList.contains(fileKey))
    {
        // 取出文件
        LDFile *ld = LDFileList[fileKey].data();
        // qDebug() << "ld->networks.networkList.size():" << ld->networks.networkList.size();
        // 获取所有网络
        ary = ld->networks->toJsonArray();
    }
    else
    {
        qDebug() << "miss fileKey:" << fileKey;
    }
    return ary;
}

// 获取文件中的所有网络，块，引脚
QJsonArray LDManager::getAllInfo(const QString &fileKey)
{
    QJsonArray ary;
    if (LDFileList.contains(fileKey))
    {
        // 取出文件
        LDFile *ld = LDFileList[fileKey].data();

        // 遍历所有网络
        for (QSharedPointer<LDNetwork> &network : ld->networks->networkMap)
        {
            // 创建网络 JSON 对象，包含基本信息
            QJsonObject networkObj = network->toJsonObject();

            // 创建元件数组
            QJsonArray componentsArray;

            // 遍历网络中的所有元件
            QMap<int, QSharedPointer<LDComponent>>::iterator itor;
            for (itor = network->componentMap.begin(); itor != network->componentMap.end(); itor++)
            {
                // 获取元件对象，已包含引脚信息
                QJsonObject componentObj = itor.value()->toJsonObject();

                // 添加到元件数组
                componentsArray.append(componentObj);
            }

            // 将元件数组添加到网络对象
            networkObj["components"] = componentsArray;

            // 将网络添加到结果数组
            ary.append(networkObj);
        }
    }
    else
    {
        qDebug() << __FILE__ << __FUNCTION__ << __LINE__ << "miss fileKey:" << fileKey;
    }
    // QJsonDocument doc(ary);
    // QString jsonString = doc.toJson(QJsonDocument::Indented);
    // LOG_INFO_DEFAULT("参数内容:\n{}", jsonString.toStdString());
    return ary;
}

QJsonArray LDManager::getComponentConnector(const QString &fileKey, int networkNumber)
{
    QJsonArray ary;
    if (LDFileList.contains(fileKey))
    {
        // 取出文件
        LDFile *ld = LDFileList[fileKey].data();
        qDebug() << "ld->networks.networkList.size():" << ld->networks->networkMap.size();
        // 获取所有网络
        if (ld->networks->networkMap.contains(networkNumber))
        {
            QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);

            // 网络的元件
            QMap<int, QSharedPointer<LDComponent>>::iterator itor;
            for (itor = network->componentMap.begin(); itor != network->componentMap.end(); itor++)
            {

                // 包括引脚
                QJsonObject objblock = itor.value()->toJsonObject();
                ary.append(objblock);
            }
        }
    }
    else
    {
        QString temp = "miss fileKey:" + fileKey;
        LOG_ERROR_DEFAULT(temp.toStdString());
    }
    return ary;
}

QJsonArray LDManager::getNetworkConnections(const QString &fileKey, int networkNumber)
{
    QJsonArray ary;
    if (LDFileList.contains(fileKey))
    {
        // 取出文件
        LDFile *ld = LDFileList[fileKey].data();
        if (ld->networks->networkMap.contains(networkNumber))
        {
            QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);
            // 判断链接所属 根据源和目的块的名称 分配到每个网络上
            for (QSharedPointer<LDConnection> &connector : ld->connections->connectionList)
            {
                QMap<int, QSharedPointer<LDComponent>>::iterator itor;
                for (itor = network->componentMap.begin(); itor != network->componentMap.end(); itor++)
                {
                    // 按源来索引
                    if (connector->SourceComponentNumber == itor.value()->Number)
                    {
                        QJsonObject objconnection = connector->toJsonObject();
                        ary.append(objconnection);
                    }
                }
            }
        }
    }
    else
    {
        qDebug() << "miss fileKey:" << fileKey;
    }
    return ary;
}

QJsonArray LDManager::getAllBlockName(const QString &fileKey)
{
    QJsonArray objArr;
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        for (auto &component : ld->components->componentMap)
        {
            if (component->Type == "Block")
            {
                QJsonObject obj;
                obj["Name"] = component->Name;
                obj["Number"] = component->Number;
                obj["ChildType"] = component->ChildType;
                obj["TaskName"] = component->TaskName;
                obj["TaskOrderNumber"] = component->TaskOrderNumber;
                obj["AuxContent"] = component->AuxContent;

                objArr.append(obj);
            }
        }
    }
    return objArr;
}

bool LDManager::deleteNetwork(const QString &fileKey, int number)
{
    LOG_INFO_DEFAULT("删除网络{}", number);
    bool flag = false;
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        // 找到对应网络
        if (ld->networks->networkMap.contains(number))
        {
            for (auto network : ld->networks->networkMap)
            {
                if (network->Number == number)
                {
                    QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(number);
                    // 删除元件
                    for (auto &component : ld->components->componentMap)
                    {
                        // 找到所含元件
                        if (component->NetworkNumber == network->Number)
                        {
                            ld->components->componentMap.remove(component->Number);
                            // 删除连接
                            for (auto &connection : ld->connections->connectionList)
                            {
                                if (connection->SourceComponentNumber == component->Number ||
                                    connection->TargetComponentNumber == component->Number)
                                {
                                    ld->connections->connectionList.removeOne(connection);
                                }
                            }
                        }
                    }
                }
                else if (network->Number > number)
                {
                    network->Number = network->Number - 1;
                }
            }
            // 删除网络
            ld->networks->networkMap.remove(number);
            // 重排页码序号
            ld->networks->sortNetworkList();
            // 插入网络，调整元件对应的NetworkNumber
            for (QSharedPointer<LDComponent> &component : ld->components->componentMap)
            {
                if (component->network != nullptr)
                {
                    // 对应到新的NetworkNumber
                    component->NetworkNumber = component->network->Number;
                }
            }
            flag = true;
        }
    }
    return flag;
}

bool LDManager::modifyNetworkLabel(const QString &fileKey, int number, const QString &newLabel)
{
    bool flag = false;
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();

        // 查找是否有重名的
        bool findSame = false;
        for (QSharedPointer<LDNetwork> &network : ld->networks->networkMap)
        {
            // 找到对应网络
            if (number != network->Number && network->Label == newLabel)
            {
                findSame = true;
                break;
            }
        }
        if (!findSame)
        {
            for (QSharedPointer<LDNetwork> &network : ld->networks->networkMap)
            {
                // 找到对应网络
                if (number == network->Number)
                {
                    network->Label = newLabel;

                    flag = true;
                    break;
                }
            }
        }
    }
    return flag;
}

bool LDManager::modifyNetworkComment(const QString &fileKey, int number, const QString &newComment)
{
    bool flag = false;
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();

        // 查找是否有重名的
        bool findSame = false;
        for (QSharedPointer<LDNetwork> &network : ld->networks->networkMap)
        {
            // 找到对应网络
            if (number != network->Number && network->Comment == newComment)
            {
                findSame = true;
                break;
            }
        }
        if (!findSame)
        {
            for (QSharedPointer<LDNetwork> &network : ld->networks->networkMap)
            {
                // 找到对应网络
                if (number == network->Number)
                {
                    network->Comment = newComment;

                    flag = true;
                    break;
                }
            }
        }
    }
    return flag;
}

bool LDManager::modifyNetworkEnable(const QString &fileKey, int number, const bool enable)
{
    bool flag = false;
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        // 找到对应网络
        if (ld->networks->networkMap.contains(number))
        {
            QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(number);
            network->Enable = enable;

            // 所属元件Enable同步
            QMap<int, QSharedPointer<LDComponent>>::iterator itor;
            for (itor = network->componentMap.begin(); itor != network->componentMap.end(); itor++)
            {
                itor.value()->Enable = network->Enable;
            }

            flag = true;
        }
    }
    return flag;
}

bool LDManager::modifyComponentName(const QString &fileKey, int compNumber, const QString &newName)
{
    bool flag = false;
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();

        // 查找是否有重名的
        bool findSame = false;
        for (QSharedPointer<LDComponent> &component : ld->components->componentMap)
        {
            // 找到对应元件
            if (compNumber != component->Number && component->Name == newName)
            {
                findSame = true;
                break;
            }
        }
        if (!findSame)
        {
            for (QSharedPointer<LDComponent> &component : ld->components->componentMap)
            {
                // 找到对应元件
                if (compNumber == component->Number)
                {
                    component->Name = newName;

                    flag = true;
                    break;
                }
            }
        }
    }
    return flag;
}

// 修改元件的引脚的类型
bool LDManager::modifyComponentConnectorType(const QString &fileKey, int compNumber, int pinId, const QString &DataType)
{
    bool flag = false;
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        if (ld->components->componentMap.contains(compNumber))
        {
            QSharedPointer<LDComponent> component = ld->searchComponentFromNumber(compNumber);
            for (QSharedPointer<LDConnector> &connector : component->connectorList)
            {
                if (connector->PinId == pinId)
                {
                    connector->DataType = DataType;
                    flag = true;
                    break;
                }
                flag = true;
            }
        }
    }
    return flag;
}

bool LDManager::modifyVariableComponent(const QString &fileKey, int networkNumber, int compNumber, const QString &scope,
                                        const QString &name, const QString &owned)
{
    bool flag = false;
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<LDComponent> com = fbd->searchComponentFromNumber(compNumber);
            com->ChildType = scope;   // 作用域
            com->AuxContent = name;   // 变量名
            com->AuxContent1 = owned; // 所属文件

            flag = true;
            QJsonArray ary;
            QJsonObject obj1;
            obj1["action"] = "modify";
            obj1["name"] = "ChildType";
            obj1["value"] = com->ChildType;
            ary.append(obj1);

            QJsonObject obj2;
            obj2["action"] = "modify";
            obj2["name"] = "AuxContent";
            obj2["value"] = com->AuxContent;
            ary.append(obj2);

            QJsonObject obj3;
            obj3["action"] = "modify";
            obj3["name"] = "AuxContent1";
            obj3["value"] = com->AuxContent1;
            ary.append(obj3);

            // emit componentChanged(fileKey, com->NetworkNumber, com->Number, ary);

            // 对应链接同步更改
            QJsonArray aryLine;
            for (QSharedPointer<LDConnection> &conn : fbd->connections->connectionList)
            {
                if (conn->SourceComponentNumber == com->Number)
                {
                    if (com->AuxContent != "???")
                    {
                        conn->VarName = com->AuxContent;
                        conn->VarOwned = com->AuxContent1;
                    }
                    else
                    {
                        conn->VarName = "VAR_" + conn->InstanceName;
                        conn->VarOwned = fbd->Name + "." + fbd->Code;
                    }

                    QJsonObject objLine;
                    objLine["action"] = "modify";
                    objLine["SourceComponentNumber"] = conn->SourceComponentNumber;
                    objLine["SourcePinId"] = conn->SourcePinId;
                    objLine["TargetComponentNumber"] = conn->TargetComponentNumber;
                    objLine["TargetPinId"] = conn->TargetPinId;
                    aryLine.append(objLine);

                    break;
                }
                if (conn->TargetComponentNumber == com->Number)
                {
                    if (com->AuxContent != "???")
                    {
                        conn->VarName = com->AuxContent;
                        conn->VarOwned = com->AuxContent1;
                    }
                    else
                    {
                        conn->VarName = "VAR_" + conn->InstanceName;
                        conn->VarOwned = fbd->Name + "." + fbd->Code;
                    }

                    QJsonObject objLine;
                    objLine["action"] = "modify";
                    objLine["SourceComponentNumber"] = conn->SourceComponentNumber;
                    objLine["SourcePinId"] = conn->SourcePinId;
                    objLine["TargetComponentNumber"] = conn->TargetComponentNumber;
                    objLine["TargetPinId"] = conn->TargetPinId;
                    aryLine.append(objLine);

                    break;
                }
            }
            // emit lineChanged(fileKey, com->NetworkNumber, aryLine);
        }
    }
    return flag;
}

bool LDManager::clearVariableComponent(const QString &fileKey, int networkNumber, int compNumber)
{
    return modifyVariableComponent(fileKey, networkNumber, compNumber, "???", "???", "???");
}

/**
 * @brief 添加连接 链接两个引脚
 * @param sourceCompNumber out引脚源块编号
 * @param sourcePinId out引脚编号
 * @param targetCompNumber in引脚目标块编号
 * @param targetPinId in引脚编号
 * @param visible 是否可见
 */
QSharedPointer<LDConnection> LDManager::addConnection(const QString &fileKey, int sourceCompNumber, int sourcePinId,
                                                      int sourceConnectIndex, int targetCompNumber, int targetPinId,
                                                      int targetConnectIndex, bool visible)
{
    LOG_INFO_DEFAULT("设置元件{}的索引{}和引脚{}->元件{}的索引{}和引脚{}连接", sourceCompNumber, sourceConnectIndex,
                     sourcePinId, targetCompNumber, targetConnectIndex, targetPinId);

    // 检查源和目标是否相同(自我连接)
    if (sourceCompNumber == targetCompNumber)
    {
        LOG_ERROR_DEFAULT("不允许元件自我连接: 元件={}", sourceCompNumber);
        return false;
    }

    // 检查文件是否存在
    if (!LDFileList.contains(fileKey))
    {
        return false;
    }
    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    // 获取文件
    LDFile *ld = cFile.data();

    // 查找源块和目标块
    QSharedPointer<LDComponent> sourceComponent = nullptr;
    QSharedPointer<LDComponent> targetComponent = nullptr;
    // 查找源块和目标块的引脚
    QSharedPointer<LDConnector> sourceConnector = nullptr;
    QSharedPointer<LDConnector> targetConnector = nullptr;

    // 查找源块
    if (ld->components->componentMap.contains(sourceCompNumber))
    {
        // 获取源块
        sourceComponent = ld->searchComponentFromNumber(sourceCompNumber);
        // 遍历源块的引脚
        for (QSharedPointer<LDConnector> &connector : sourceComponent->connectorList)
        {
            if (connector->PinId == sourcePinId)
            {
                // 找到源块的引脚
                sourceConnector = connector;
                break;
            }
        }
    }

    // 查找目标块
    if (ld->components->componentMap.contains(targetCompNumber))
    {
        // 获取目标块
        targetComponent = ld->searchComponentFromNumber(targetCompNumber);
        // 遍历目标块的引脚
        for (QSharedPointer<LDConnector> &connector : targetComponent->connectorList)
        {
            if (connector->PinId == targetPinId)
            {
                // 找到目标块的引脚
                targetConnector = connector;
                break;
            }
        }
    }

    // 检查源或目标元件是否不存在
    if (!sourceComponent || !targetComponent)
    {
        LOG_ERROR_DEFAULT("源或目标元件不存在: 源={}, 目标={}", sourceCompNumber, targetCompNumber);
        return false;
    }

    // 检查源或目标引脚是否不存在
    if (!sourceConnector || !targetConnector)
    {
        LOG_ERROR_DEFAULT("源或目标引脚不存在: 源引脚={}, 目标引脚={}", sourcePinId, targetPinId);
        return false;
    }

    // 判断是否索引已经存在连接
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        // 确认是否已经存在完全相同的连接
        if ((connection->SourceComponentNumber == sourceCompNumber &&
             connection->SourceConnectIndex == sourceConnectIndex) ||
            (connection->TargetComponentNumber == targetCompNumber &&
             connection->TargetConnectIndex == targetConnectIndex))
        {
            LOG_ERROR_DEFAULT("存在连接: 源元件{}的索引{}->元件{}的索引{}", connection->SourceComponentNumber,
                              connection->SourceConnectIndex, connection->TargetComponentNumber,
                              connection->TargetConnectIndex);
            return false;
        }
    }

    // 如果引脚不存在完全相同的连接,则创建新的连接
    QString strUUID = QUuid::createUuid().toString().remove("{").remove("}").remove("-");
    QString instanceName = strUUID;
    QString sourceName;
    QString sourceDataType;
    int sourceComponentNumber = 0;
    int sourcePinID = 0;

    QString targetName;
    QString targetDataType;
    int targetComponentNumber = 0;
    int targetPinID = 0;

    int startx = 0, starty = 0, endx = 0, endy = 0;
    // 1.判断源块和目标块的引脚方向
    if (targetConnector->Direction == "Input")
    {
        sourceName = sourceConnector->Name;
        sourceDataType = sourceConnector->DataType;
        sourceComponentNumber = sourceComponent->Number;
        sourcePinID = sourceConnector->PinId;

        targetName = targetConnector->Name;
        targetDataType = targetConnector->DataType;
        targetComponentNumber = targetComponent->Number;
        targetPinID = targetConnector->PinId;
    }
    else if (sourceConnector->Direction == "Input")
    {
        sourceName = targetConnector->Name;
        sourceDataType = targetConnector->DataType;
        sourceComponentNumber = targetComponent->Number;
        sourcePinID = targetConnector->PinId;

        targetName = sourceConnector->Name;
        targetDataType = sourceConnector->DataType;
        targetComponentNumber = sourceComponent->Number;
        targetPinID = sourceConnector->PinId;
    }
    // 2.创建新的连接
    QSharedPointer<LDConnection> newConnection = QSharedPointer<LDConnection>(new LDConnection());
    // 检查源引脚是否已有其他连接（连接到任何目标）结果如果存在，重用现有连接的实例名称和变量信息
    QStringList oldInstanceName = haveConnectionFromSourcePin(fileKey, sourceComponentNumber, sourcePinID);
    if (oldInstanceName.size() > 0)
    {
        newConnection->InstanceName = oldInstanceName[0];
        newConnection->VarName = oldInstanceName[1];
        newConnection->VarOwned = oldInstanceName[2];
    }
    else
    {
        newConnection->InstanceName = instanceName;
        newConnection->VarName = instanceName + "_VAR";
        newConnection->VarOwned = ld->Name + "." + ld->Code;
    }
    // 设置新连接的源信息
    newConnection->SourceDataType = sourceDataType;
    newConnection->SourceComponentNumber = sourceComponentNumber;
    newConnection->SourcePinId = sourcePinID;
    newConnection->SourceConnectIndex = sourceConnectIndex;

    // 设置新连接的目标信息
    newConnection->TargetDataType = targetDataType;
    newConnection->TargetComponentNumber = targetComponentNumber;
    newConnection->TargetPinId = targetPinID;
    newConnection->TargetConnectIndex = targetConnectIndex;
    // 将新连接添加到连接列表
    LOG_INFO_DEFAULT("将元件{}的{}->元件{}的{}连接添加到连接列表", sourceComponentNumber, sourceConnectIndex,
                     targetComponentNumber, targetConnectIndex);
    ld->connections->connectionList.append(newConnection);

    return newConnection;
}

// 断开连接
bool LDManager::deleteConnection(const QString &fileKey, int sourceCompNumber, int sourcePinId, int targetCompNumber,
                                 int targetPinId)
{
    // 检查文件是否存在
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        // 获取文件
        LDFile *ld = cFile.data();

        // 查找源块和目标块
        QSharedPointer<LDComponent> sourceComponent = nullptr;
        QSharedPointer<LDComponent> targetComponent = nullptr;
        // 查找源块和目标块的引脚
        QSharedPointer<LDConnector> sourceConnector = nullptr;
        QSharedPointer<LDConnector> targetConnector = nullptr;

        // 查找源块
        if (ld->components->componentMap.contains(sourceCompNumber))
        {
            // 获取源块
            sourceComponent = ld->searchComponentFromNumber(sourceCompNumber);
            // 遍历源块的引脚
            for (QSharedPointer<LDConnector> &connector : sourceComponent->connectorList)
            {
                if (connector->PinId == sourcePinId)
                {
                    // 找到源块的引脚
                    sourceConnector = connector;
                    break;
                }
            }
        }

        // 查找目标块
        if (ld->components->componentMap.contains(targetCompNumber))
        {
            // 获取目标块
            targetComponent = ld->searchComponentFromNumber(targetCompNumber);
            // 遍历目标块的引脚
            for (QSharedPointer<LDConnector> &connector : targetComponent->connectorList)
            {
                if (connector->PinId == targetPinId)
                {
                    // 找到目标块的引脚
                    targetConnector = connector;
                    break;
                }
            }
        }

        // 如果找到了源块和目标块的引脚，在连接列表中查找并删除连接
        if (sourceConnector && targetConnector)
        {
            for (int i = 0; i < ld->connections->connectionList.size(); i++)
            {
                QSharedPointer<LDConnection> &connection = ld->connections->connectionList[i];
                // 确认是否存在完全相同的连接（相同源引脚连接到相同目标引脚）
                if (connection->SourceComponentNumber == sourceCompNumber && connection->SourcePinId == sourcePinId &&
                    connection->TargetComponentNumber == targetCompNumber && connection->TargetPinId == targetPinId)
                {
                    // 从连接列表中移除该连接
                    ld->connections->connectionList.removeAt(i);

                    // 重新排序连接索引
                    QList<int> affectedComponents;
                    affectedComponents.append(sourceCompNumber);
                    affectedComponents.append(targetCompNumber);
                    reorderConnectionIndexes(fileKey, affectedComponents);

                    return true;
                }
            }
        }
    }
    return false;
}

// 水平串联触点 触点块由AND块和变量块组成
int LDManager::addSeriesContact(const QString &fileKey, int compNumber, int leftOrRight)
{
    LOG_INFO_DEFAULT("串联触点到{}元件的{}边", compNumber, leftOrRight == 0 ? "左" : "右");
    // 检查文件是否存在
    if (!LDFileList.contains(fileKey))
    {
        return -1;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 检查插入位置元件是否存在
    QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
    if (!com_ptr)
    {
        LOG_ERROR_DEFAULT("元件不存在");
        return -1;
    }

    // 判断插入位置是否为起始常量元件且触点串联位置是否合法
    if (com_ptr->Type == "Variable" && com_ptr->ChildType == "Constant" && com_ptr->AuxContent == "Start")
    {
        if (leftOrRight != 2)
        {
            LOG_ERROR_DEFAULT("触点只能串联到起始常量元件右边");
            return -1;
        }
    }
    QSharedPointer<LDComponent> andBlock = nullptr;
    // 在元件左侧添加水平串联触点
    if (leftOrRight == 0)
    {
        andBlock = addSeriesContactToLeft(fileKey, com_ptr, "Conatct");
        if (!andBlock)
        {
            LOG_ERROR_DEFAULT("插入元件左侧串联AND块失败");
            return -1;
        }
    }
    // 在元件右侧添加水平串联元件
    else if (leftOrRight == 2)
    {
        andBlock = addSeriesContactToRight(fileKey, com_ptr, "Conatct");
        if (!andBlock)
        {
            LOG_ERROR_DEFAULT("插入元件右侧串联AND块失败");
            return -1;
        }
    }
    else
    {
        LOG_ERROR_DEFAULT("插入位置不合法");
        return -1;
    }

    // 构造变量块并连接到AND块
    if (!connectVariableToComponent(fileKey, andBlock))
    {
        LOG_ERROR_DEFAULT("连接变量块到AND块失败");
        return -1;
    }
    LOG_INFO_DEFAULT("成功水平串联触点到元件{}，返回新AND块的编号{}", compNumber, andBlock->Number);
    return andBlock->Number;
}

// 复制触点/线圈/跳转/块/...等元件(包含挂载变量和引脚信息)
QJsonObject LDManager::copyComponent(const QString &fileKey, int compNumber)
{
    LOG_INFO_DEFAULT("复制元件 compNumber={}", compNumber);

    // 检查文件是否存在
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return {};
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 检查元件是否存在
    QSharedPointer<LDComponent> component = ld->searchComponentFromNumber(compNumber);
    if (!component)
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return {};
    }

    // 创建元件数据结构
    QJsonObject componentDataObj;
    QJsonArray componentsArray;

    // 添加主元件到剪贴板
    QJsonObject mainComponentObj = component->toJsonObject();
    componentsArray.append(mainComponentObj);

    // 查找并添加相关的变量元件
    QList<QSharedPointer<LDComponent>> relatedVariables = findRelatedVariableComponents(fileKey, compNumber);
    for (const auto &varComponent : relatedVariables)
    {
        QJsonObject varComponentObj = varComponent->toJsonObject();
        componentsArray.append(varComponentObj);
    }

    // 过滤主元件的引脚信息，只保留与relatedVariables变量相关联的引脚
    QJsonArray originalConnectors = mainComponentObj["connectors"].toArray();
    QJsonArray filteredConnectors;
    int filteredCount = 0;

    LOG_INFO_DEFAULT("originalConnectors size :{}", originalConnectors.size());
    for (int i = 0; i < originalConnectors.size(); i++)
    {
        QJsonObject connectorObj = originalConnectors[i].toObject();
        int pinId = connectorObj["PinId"].toInt();
        bool isRelatedToVariable = false;

        // 检查该引脚是否与relatedVariables中的变量元件有连接
        for (const auto &connection : ld->connections->connectionList)
        {
            // 检查作为源引脚的连接
            if (connection->SourceComponentNumber == compNumber && connection->SourcePinId == pinId)
            {
                QSharedPointer<LDComponent> targetComponent =
                    ld->searchComponentFromNumber(connection->TargetComponentNumber);
                if (targetComponent && relatedVariables.contains(targetComponent))
                {
                    isRelatedToVariable = true;
                    break;
                }
            }
            // 检查作为目标引脚的连接
            else if (connection->TargetComponentNumber == compNumber && connection->TargetPinId == pinId)
            {
                QSharedPointer<LDComponent> sourceComponent =
                    ld->searchComponentFromNumber(connection->SourceComponentNumber);
                if (sourceComponent && relatedVariables.contains(sourceComponent))
                {
                    isRelatedToVariable = true;
                    break;
                }
            }
        }

        // 如果引脚与relatedVariables相关联，则保留该引脚
        if (isRelatedToVariable)
        {
            filteredConnectors.append(connectorObj);
        }
        else
        {
            filteredCount++;
        }
    }
    LOG_INFO_DEFAULT("filteredConnectors size :{}", filteredConnectors.size());
    // 更新主元件的引脚信息
    mainComponentObj["connectors"] = filteredConnectors;
    // 更新componentsArray中的主元件对象
    componentsArray[0] = mainComponentObj;

    LOG_INFO_DEFAULT("relatedVariables size :{}", relatedVariables.size());
    QJsonArray connectionsArray;
    for (const auto &varComponent : relatedVariables)
    {
        QSharedPointer<LDConnection> conn = getConnection(fileKey, varComponent->Number, compNumber);
        if (conn)
        {
            LOG_INFO_DEFAULT("varComponent->Number:{}, compNumber:{}", varComponent->Number, compNumber);
            QJsonObject connectionObj = conn->toJsonObject();
            connectionsArray.append(connectionObj);
        }
    }

    // 保存元件数据和连接信息
    componentDataObj["components"] = componentsArray;
    componentDataObj["connections"] = connectionsArray;
    componentDataObj["mainComponentNumber"] = compNumber;
    componentDataObj["componentCount"] = componentsArray.size();
    componentDataObj["connectionCount"] = connectionsArray.size();

    // 打印componentDataObj
    LOG_INFO_DEFAULT("元件数据: {}",
                     QString(QJsonDocument(componentDataObj).toJson(QJsonDocument::Compact)).toStdString());

    return componentDataObj;
}

// 剪切触点/线圈/跳转/块/...等元件(包含挂载变量和引脚信息)
QJsonObject LDManager::cutComponent(const QString &fileKey, int compNumber)
{
    LOG_INFO_DEFAULT("剪切元件 compNumber={}", compNumber);

    // 检查文件是否存在
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return QJsonObject();
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 检查元件是否存在
    QSharedPointer<LDComponent> component = ld->searchComponentFromNumber(compNumber);
    if (!component)
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return QJsonObject();
    }

    // 检查是否为起始常量元件（不允许剪切）
    if (component->Type == "Variable" && component->ChildType == "Constant" && component->AuxContent == "Start")
    {
        LOG_ERROR_DEFAULT("不允许剪切起始常量元件");
        return QJsonObject();
    }

    // 第一步：执行复制操作
    QJsonObject componentData = copyComponent(fileKey, compNumber);
    if (componentData.isEmpty())
    {
        LOG_ERROR_DEFAULT("复制元件失败，剪切操作中止");
        return QJsonObject();
    }

    LOG_INFO_DEFAULT("复制元件成功，开始删除操作");

    // 第二步：根据元件类型执行相应的删除操作
    bool deleteSuccess = false;

    // 判断元件类型并调用相应的删除函数
    if (component->Type == "Block")
    {
        if (component->ChildType == "Func")
        {
            if (component->AuxContent == "Conatct")
            {
                // 触点类型（AND）
                LOG_INFO_DEFAULT("删除{} {}元件", component->Number, component->AuxContent.toStdString());
                deleteSuccess = deleteContact(fileKey, compNumber);
            }
            else if (component->AuxContent == "Coil" || component->AuxContent == "Set1" ||
                     component->AuxContent == "Set0" || component->AuxContent == "Jump" ||
                     component->AuxContent == "Return")
            {
                // 线圈/跳转/返回类型
                LOG_INFO_DEFAULT("删除{} {}元件", component->Number, component->AuxContent.toStdString());
                deleteSuccess = deleteCoilComponent(fileKey, component->NetworkNumber, compNumber);
            }
            else
            {
                // 其他功能块类型 用户自定义块
                LOG_INFO_DEFAULT("删除{} {}元件", component->Number, component->AuxContent.toStdString());
                deleteSuccess = deleteBlockComponent(fileKey, component->NetworkNumber, compNumber);
            }
        }
        else
        {
            // FB类型或其他块类型 用户自定义块
            LOG_INFO_DEFAULT("删除{} {}元件", component->Number, component->ChildType.toStdString());
            deleteSuccess = deleteBlockComponent(fileKey, component->NetworkNumber, compNumber);
        }
    }
    else
    {
        LOG_ERROR_DEFAULT("删除元件类型错误: Type={}, ChildType={}, AuxContent={}", component->Type.toStdString(),
                          component->ChildType.toStdString(), component->AuxContent.toStdString());
        return QJsonObject();
    }

    // 第三步：处理删除结果
    if (deleteSuccess)
    {
        LOG_INFO_DEFAULT("成功剪切元件 {}", compNumber);
        return componentData;
    }
    else
    {
        LOG_ERROR_DEFAULT("删除元件失败，剪切操作失败");
        return QJsonObject();
    }
}

// 根据传入的触点信息添加触点
bool addSeriesContactByInfo(const QString &fileKey, int compNumber, int leftOrRight, QJsonObject param)
{
    return true;
}

// 验证剪贴板数据是否为串联触点类型（And块）
bool LDManager::isSeriesContactType(const QJsonObject &componentJson)
{
    QString type = componentJson["Type"].toString();
    QString childType = componentJson["ChildType"].toString();
    QString auxContent = componentJson["AuxContent"].toString();

    return (type == "Block" && childType == "Func" && auxContent == "Conatct");
}

// 验证剪贴板数据是否为并联触点类型（Or块）
bool LDManager::isParallelContactType(const QJsonObject &componentJson)
{
    QString type = componentJson["Type"].toString();
    QString childType = componentJson["ChildType"].toString();
    QString auxContent = componentJson["AuxContent"].toString();

    return (type == "Block" && childType == "Func" && auxContent == "Or");
}

// 验证剪贴板数据是否为线圈/跳转/返回类型
bool LDManager::isCoilJumpReturnType(const QJsonObject &componentJson)
{
    QString type = componentJson["Type"].toString();
    QString childType = componentJson["ChildType"].toString();
    QString auxContent = componentJson["AuxContent"].toString();

    return (type == "Block" && childType == "Func" &&
            (auxContent == "Coil" || auxContent == "Jump" || auxContent == "Return" || auxContent == "Set1" ||
             auxContent == "Set0"));
}

// 验证剪贴板数据是否为块元件类型（FB/FUNC/ADVANCE）
bool LDManager::isBlockComponentType(const QJsonObject &componentJson)
{
    QString type = componentJson["Type"].toString();
    QString childType = componentJson["ChildType"].toString();

    return (type == "Block" && (childType == "FB" || childType == "FUNC" || childType == "ADVANCE"));
}

// 根据JSON中的connectors信息查找或创建引脚
QSharedPointer<LDConnector> LDManager::findOrCreateConnectorFromJson(QSharedPointer<LDComponent> component, int pinId,
                                                                     const QJsonArray &componentsArray)
{
    // 首先检查元件中是否已存在该引脚
    QSharedPointer<LDConnector> existingConnector = component->searchConnectorFromPinId(pinId);
    if (existingConnector)
    {
        return existingConnector;
    }

    // 在JSON的components数组中查找对应元件的connectors信息
    for (const QJsonValue &componentValue : componentsArray)
    {
        QJsonObject componentJson = componentValue.toObject();
        if (componentJson["Number"].toInt() == component->Number ||
            (componentJson["Type"].toString() == component->Type &&
             componentJson["ChildType"].toString() == component->ChildType))
        {
            // 在该元件的connectors中查找对应PinId的引脚信息
            QJsonArray connectorsArray = componentJson["connectors"].toArray();
            for (const QJsonValue &connectorValue : connectorsArray)
            {
                QJsonObject connectorJson = connectorValue.toObject();
                if (connectorJson["PinId"].toInt() == pinId)
                {
                    // 根据JSON信息创建引脚
                    QSharedPointer<LDConnector> newConnector = QSharedPointer<LDConnector>(new LDConnector());
                    newConnector->PinId = connectorJson["PinId"].toInt();
                    newConnector->Direction = connectorJson["Direction"].toString();
                    newConnector->Name = connectorJson["Name"].toString();
                    newConnector->DataType = connectorJson["DataType"].toString();
                    newConnector->DataTypeGroup = connectorJson["DataTypeGroup"].toString();
                    newConnector->Negated = connectorJson["Negated"].toBool();
                    newConnector->Comment = connectorJson["Comment"].toString();
                    newConnector->FastSignal = connectorJson["FastSignal"].toBool();
                    newConnector->InitValue = connectorJson["InitValue"].toString();
                    newConnector->isInitVar = connectorJson["isInitVar"].toBool();
                    newConnector->XPos = connectorJson["XPos"].toInt();
                    newConnector->YPos = connectorJson["YPos"].toInt();
                    newConnector->Visible = connectorJson["Visible"].toBool();
                    newConnector->IsLogical = connectorJson["IsLogical"].toBool();
                    newConnector->ChildNumber = connectorJson["ChildNumber"].toInt();
                    newConnector->SupportChangeDataType = connectorJson["SupportChangeDataType"].toBool();
                    newConnector->NewAdd = connectorJson["NewAdd"].toBool();

                    // 生成新的InstanceName
                    QString strUUID = QUuid::createUuid().toString().remove("{").remove("}").remove("-");
                    newConnector->InstanceName = connectorJson["InstanceName"].toString() + "_" + strUUID.left(8);

                    component->connectorList.append(newConnector);
                    LOG_INFO_DEFAULT("根据JSON信息创建引脚: 元件{}, PinId={}, Name={}, Direction={}, DataType={}",
                                     component->Number, pinId, newConnector->Name.toStdString(),
                                     newConnector->Direction.toStdString(), newConnector->DataType.toStdString());
                    return newConnector;
                }
            }
        }
    }

    // 如果在JSON中找不到对应的引脚信息，则使用默认方式创建（保持向后兼容）
    QSharedPointer<LDConnector> defaultConnector = QSharedPointer<LDConnector>(new LDConnector());
    defaultConnector->PinId = pinId;
    if (pinId < 0)
    {
        defaultConnector->Direction = "Output";
        defaultConnector->Name = "OUT" + QString::number(qAbs(pinId));
    }
    else
    {
        defaultConnector->Direction = "Input";
        defaultConnector->Name = "IN" + QString::number(pinId);
    }
    defaultConnector->DataType = "BOOL";
    defaultConnector->Visible = true;
    QString strUUID = QUuid::createUuid().toString().remove("{").remove("}").remove("-");
    defaultConnector->InstanceName = (pinId < 0 ? "OUT_" : "IN_") + strUUID;

    component->connectorList.append(defaultConnector);
    LOG_WARN_DEFAULT("未在JSON中找到PinId={}的引脚信息，使用默认方式创建: 元件{}, Name={}", pinId, component->Number,
                     defaultConnector->Name.toStdString());
    return defaultConnector;
}

// 水平粘贴触点
bool LDManager::pasteSeriesContact(const QString &fileKey, int compNumber, const QJsonObject &componentData,
                                   int leftOrRight)
{

    LOG_INFO_DEFAULT("元件数据: {}",
                     QString(QJsonDocument(componentData).toJson(QJsonDocument::Compact)).toStdString());

    // 检查元件数据是否有效
    if (componentData.isEmpty())
    {
        LOG_ERROR_DEFAULT("元件数据为空，无法粘贴");
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    if (!cFile)
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return false;
    }
    LDFile *ld = cFile.data();
    if (!ld)
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return false;
    }
    QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
    if (!com_ptr)
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return false;
    }
    int networkNumber = com_ptr->NetworkNumber;
    QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);
    if (!network)
    {
        LOG_ERROR_DEFAULT("网络不存在: networkNumber={}", networkNumber);
        return false;
    }

    QSharedPointer<LDComponent> newCom = nullptr;

    // 获取传入的主元件数据
    QJsonObject mainComponent = componentData["components"].toArray()[0].toObject();

    if (mainComponent["Type"].toString() == "Block" && mainComponent["ChildType"].toString() == "Func" &&
        mainComponent["AuxContent"].toString() == "Conatct")
    {
        // 处理左侧串联触点
        if (leftOrRight == 0)
        {
            newCom = addSeriesContactToLeft(fileKey, com_ptr, "Conatct");
            if (!newCom)
            {
                LOG_ERROR_DEFAULT("插入元件串联AND块失败");
                return false;
            }
        }
        // 处理右侧串联触点
        else if (leftOrRight == 2)
        {
            newCom = addSeriesContactToRight(fileKey, com_ptr, "Conatct");
            if (!newCom)
            {
                LOG_ERROR_DEFAULT("插入元件串联AND块失败");
                return false;
            }
        }
    }
    else
    {
        LOG_ERROR_DEFAULT("水平粘贴触点失败, Type = {}, ChildType = {}, AuxContent = {}",
                          mainComponent["Type"].toString().toStdString(),
                          mainComponent["ChildType"].toString().toStdString(),
                          mainComponent["AuxContent"].toString().toStdString());
        return false;
    }

    LOG_INFO_DEFAULT("水平粘贴触点 = {}", newCom->Number);

    // 获取传入数据赋值给newCom
    QJsonObject newComJson = componentData["components"].toArray()[0].toObject();

    // 保存原始编号用于后续映射更新
    int originalNewComNumber = newCom->Number;
    int targetNewComNumber = newComJson["Number"].toInt();

    newCom->Type = newComJson["Type"].toString();
    newCom->ChildType = newComJson["ChildType"].toString();
    newCom->AuxContent = newComJson["AuxContent"].toString();
    newCom->InstanceName = newComJson["InstanceName"].toString();
    newCom->Comment = newComJson["Comment"].toString();
    newCom->Name = newComJson["Name"].toString();
    newCom->DataType_Local = newComJson["DataType_Local"].toString();
    newCom->Source = newComJson["Source"].toString();
    newCom->Comment = newComJson["Comment"].toString();
    newCom->SupportsInPinType = newComJson["SupportsInPinType"].toString();
    newCom->SupportsOutPinType = newComJson["SupportsOutPinType"].toString();
    newCom->SupportsInPinAdd = newComJson["SupportsInPinAdd"].toBool();
    newCom->SupportsOutPinAdd = newComJson["SupportsOutPinAdd"].toBool();
    newCom->Width = newComJson["Width"].toInt();
    newCom->Height = newComJson["Height"].toInt();

    // 获取传入数据中的连接信息
    QJsonArray connectionsArray = componentData["connections"].toArray();

    // 创建元件编号映射表（原始编号 -> 新编号）
    QMap<int, int> componentNumberMap;
    int originalMainNumber = componentData["mainComponentNumber"].toInt();
    componentNumberMap[originalMainNumber] = newCom->Number;

    // 创建并连接变量元件（如果传入数据中有相关变量）
    QJsonArray componentsArray = componentData["components"].toArray();
    for (int i = 1; i < componentsArray.size(); ++i)
    {
        QJsonObject varComponentJson = componentsArray[i].toObject();
        if (varComponentJson["Type"].toString() == "Variable")
        {
            QSharedPointer<LDComponent> varComponent =
                createComponentFromClipboard(varComponentJson, fileKey, networkNumber);
            if (varComponent)
            {
                network->componentMap.insert(varComponent->Number, varComponent);
                ld->components->componentMap.insert(varComponent->Number, varComponent);

                // 记录原始编号到新编号的映射
                int originalVarNumber = varComponentJson["Number"].toInt();
                componentNumberMap[originalVarNumber] = varComponent->Number;
            }
        }
    }

    // 恢复原始的连接关系，保持PinId顺序, 保持SourceConnectIndex和TargetConnectIndex一致
    componentsArray = componentData["components"].toArray();
    for (const QJsonValue &connectionValue : connectionsArray)
    {
        QJsonObject connectionJson = connectionValue.toObject();
        int originalSourceNumber = connectionJson["SourceComponentNumber"].toInt();
        int originalTargetNumber = connectionJson["TargetComponentNumber"].toInt();
        int sourcePinId = connectionJson["SourcePinId"].toInt();
        int targetPinId = connectionJson["TargetPinId"].toInt();
        int sourceConnectIndex = connectionJson["SourceConnectIndex"].toInt();
        int targetConnectIndex = connectionJson["TargetConnectIndex"].toInt();

        // 检查连接是否涉及变量元件和AND块
        if (componentNumberMap.contains(originalSourceNumber) && componentNumberMap.contains(originalTargetNumber))
        {
            int newSourceNumber = componentNumberMap[originalSourceNumber];
            int newTargetNumber = componentNumberMap[originalTargetNumber];

            // 确保引脚存在（使用原始的PinId）
            QSharedPointer<LDComponent> sourceComp = ld->searchComponentFromNumber(newSourceNumber);
            QSharedPointer<LDComponent> targetComp = ld->searchComponentFromNumber(newTargetNumber);

            if (sourceComp && targetComp)
            {
                // 根据JSON中的connectors信息查找或创建源引脚
                QSharedPointer<LDConnector> sourceConnector =
                    findOrCreateConnectorFromJson(sourceComp, sourcePinId, componentsArray);

                // 根据JSON中的connectors信息查找或创建目标引脚
                QSharedPointer<LDConnector> targetConnector =
                    findOrCreateConnectorFromJson(targetComp, targetPinId, componentsArray);

                QSharedPointer<LDConnection> newConnection =
                    addConnection(fileKey, newSourceNumber, sourcePinId, sourceConnectIndex, newTargetNumber,
                                  targetPinId, targetConnectIndex, true);

                if (nullptr != newConnection)
                {
                    // 如果是变量元件连接到AND块
                    if (sourceComp->Type == "Variable" && targetComp->Type == "Block")
                    {
                        sourceComp->ParentNumber = newTargetNumber;
                        sourceComp->ParentPinId = sourcePinId;
                        sourceComp->ParentPinDataType = "BOOL";
                    }

                    LOG_INFO_DEFAULT("成功恢复连接: {} (PinId:{}) -> {} (PinId:{})", newSourceNumber, sourcePinId,
                                     newTargetNumber, targetPinId);
                }
                else
                {
                    LOG_ERROR_DEFAULT("恢复连接失败: {} -> {}", newSourceNumber, newTargetNumber);
                }
            }
        }
    }

    LOG_INFO_DEFAULT("水平粘贴触点成功: 元件{}", compNumber);
    return newCom->Number;
}

// 并联粘贴触点
bool LDManager::pasteParallelContact(const QString &fileKey, int compNumber, const QJsonObject &componentData)
{
    // 检查元件数据是否有效
    if (componentData.isEmpty())
    {
        LOG_ERROR_DEFAULT("元件数据为空，无法粘贴");
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();
    QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
    int networkNumber = com_ptr->NetworkNumber;
    QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);

    if (!com_ptr)
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return false;
    }
    int newComNumber = addParallelContact(fileKey, compNumber);
    QSharedPointer<LDComponent> newCom = ld->searchComponentFromNumber(newComNumber);
    if (!newCom)
    {
        LOG_ERROR_DEFAULT("插入元件并联OR块失败");
        return false;
    }

    // 获取传入数据赋值给newCom
    QJsonObject newComJson = componentData["components"].toArray()[0].toObject();

    // 保存原始编号用于后续映射更新
    int originalNewComNumber = newCom->Number;
    int targetNewComNumber = newComJson["Number"].toInt();

    newCom->Type = newComJson["Type"].toString();
    newCom->ChildType = newComJson["ChildType"].toString();
    newCom->AuxContent = newComJson["AuxContent"].toString();
    newCom->InstanceName = newComJson["InstanceName"].toString();
    newCom->Comment = newComJson["Comment"].toString();
    newCom->Name = newComJson["Name"].toString();
    newCom->DataType_Local = newComJson["DataType_Local"].toString();
    newCom->Source = newComJson["Source"].toString();
    newCom->Comment = newComJson["Comment"].toString();
    newCom->SupportsInPinType = newComJson["SupportsInPinType"].toString();
    newCom->SupportsOutPinType = newComJson["SupportsOutPinType"].toString();
    newCom->SupportsInPinAdd = newComJson["SupportsInPinAdd"].toBool();
    newCom->SupportsOutPinAdd = newComJson["SupportsOutPinAdd"].toBool();
    newCom->Width = newComJson["Width"].toInt();
    newCom->Height = newComJson["Height"].toInt();

    // 获取传入数据中的连接信息
    QJsonArray connectionsArray = componentData["connections"].toArray();

    // 创建元件编号映射表（原始编号 -> 新编号）
    QMap<int, int> componentNumberMap;
    int originalMainNumber = componentData["mainComponentNumber"].toInt();
    componentNumberMap[originalMainNumber] = newCom->Number;

    // 创建并连接变量元件（如果传入数据中有相关变量）
    QJsonArray componentsArray = componentData["components"].toArray();
    for (int i = 1; i < componentsArray.size(); ++i)
    {
        QJsonObject varComponentJson = componentsArray[i].toObject();
        if (varComponentJson["Type"].toString() == "Variable")
        {
            QSharedPointer<LDComponent> varComponent =
                createComponentFromClipboard(varComponentJson, fileKey, networkNumber);
            if (varComponent)
            {
                network->componentMap.insert(varComponent->Number, varComponent);
                ld->components->componentMap.insert(varComponent->Number, varComponent);

                // 记录原始编号到新编号的映射
                int originalVarNumber = varComponentJson["Number"].toInt();
                componentNumberMap[originalVarNumber] = varComponent->Number;
            }
        }
    }

    // 恢复原始的连接关系，保持PinId顺序一致
    for (const QJsonValue &connectionValue : connectionsArray)
    {
        QJsonObject connectionJson = connectionValue.toObject();
        int originalSourceNumber = connectionJson["SourceComponentNumber"].toInt();
        int originalTargetNumber = connectionJson["TargetComponentNumber"].toInt();
        int sourcePinId = connectionJson["SourcePinId"].toInt();
        int targetPinId = connectionJson["TargetPinId"].toInt();
        int sourceConnectIndex = connectionJson["SourceConnectIndex"].toInt();
        int targetConnectIndex = connectionJson["TargetConnectIndex"].toInt();

        // 检查连接是否涉及变量元件和AND块
        if (componentNumberMap.contains(originalSourceNumber) && componentNumberMap.contains(originalTargetNumber))
        {
            int newSourceNumber = componentNumberMap[originalSourceNumber];
            int newTargetNumber = componentNumberMap[originalTargetNumber];

            // 确保引脚存在（使用原始的PinId）
            QSharedPointer<LDComponent> sourceComp = ld->searchComponentFromNumber(newSourceNumber);
            QSharedPointer<LDComponent> targetComp = ld->searchComponentFromNumber(newTargetNumber);

            if (sourceComp && targetComp)
            {
                // 确保源元件有对应的输出引脚
                QSharedPointer<LDConnector> sourceConnector = sourceComp->searchConnectorFromPinId(sourcePinId);
                if (!sourceConnector)
                {
                    // 如果引脚不存在，创建它（保持原始PinId）
                    sourceConnector = QSharedPointer<LDConnector>(new LDConnector());
                    sourceConnector->PinId = sourcePinId;
                    sourceConnector->Direction = "Output";
                    sourceConnector->Name = "OUT" + QString::number(qAbs(sourcePinId));
                    sourceConnector->DataType = "BOOL";
                    sourceConnector->Visible = true;
                    QString strUUID = QUuid::createUuid().toString().remove("{").remove("}").remove("-");
                    sourceConnector->InstanceName = "OUT_" + strUUID;
                    sourceComp->connectorList.append(sourceConnector);
                }

                // 确保目标元件有对应的输入引脚
                QSharedPointer<LDConnector> targetConnector = targetComp->searchConnectorFromPinId(targetPinId);
                if (!targetConnector)
                {
                    // 如果引脚不存在，创建它（保持原始PinId）
                    targetConnector = QSharedPointer<LDConnector>(new LDConnector());
                    targetConnector->PinId = targetPinId;
                    targetConnector->Direction = "Input";
                    targetConnector->Name = "IN" + QString::number(targetPinId);
                    targetConnector->DataType = "BOOL";
                    targetConnector->Visible = true;
                    QString strUUID = QUuid::createUuid().toString().remove("{").remove("}").remove("-");
                    targetConnector->InstanceName = "IN_" + strUUID;
                    targetComp->connectorList.append(targetConnector);
                }

                // 建立连接，保持原始的PinId
                QSharedPointer<LDConnection> newConnection =
                    addConnection(fileKey, newSourceNumber, sourcePinId, sourceConnectIndex, newTargetNumber,
                                  targetPinId, targetConnectIndex, true);
                if (nullptr != newConnection)
                {
                    // 设置父子关系（如果是变量元件连接到AND块）
                    if (sourceComp->Type == "Variable" && targetComp->Type == "Block")
                    {
                        sourceComp->ParentNumber = newTargetNumber;
                        sourceComp->ParentPinId = sourcePinId;
                        sourceComp->ParentPinDataType = "BOOL";
                    }

                    LOG_INFO_DEFAULT("成功恢复连接: {} (PinId:{}) -> {} (PinId:{})", newSourceNumber, sourcePinId,
                                     newTargetNumber, targetPinId);
                }
                else
                {
                    LOG_ERROR_DEFAULT("恢复连接失败: {} -> {}", newSourceNumber, newTargetNumber);
                }
            }
        }
    }

    LOG_INFO_DEFAULT("成功粘贴串联触点到元件{}，保持原始PinId连接顺序", compNumber);
    return newCom->Number;
}

// 粘贴线圈/跳转/返回/Set1/Set0
bool LDManager::pasteCoilJumpReturn(const QString &fileKey, int compNumber, const QJsonObject &componentData)
{
    // 检查元件数据是否有效
    if (componentData.isEmpty())
    {
        LOG_ERROR_DEFAULT("元件数据为空，无法粘贴");
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();
    QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
    int networkNumber = com_ptr->NetworkNumber;
    QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);

    if (!com_ptr)
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return false;
    }

    // 获取传入数据中的主元件数据
    QJsonObject mainComponent = componentData["components"].toArray()[0].toObject();

    int newComNumber = -1;
    if (mainComponent["Type"].toString() == "Block" && mainComponent["ChildType"].toString() == "Func" &&
        (mainComponent["AuxContent"].toString() == "Coil" || mainComponent["AuxContent"].toString() == "Jump" ||
         mainComponent["AuxContent"].toString() == "Return" || mainComponent["AuxContent"].toString() == "Set1" ||
         mainComponent["AuxContent"].toString() == "Set0"))
    {
        newComNumber = addCoilComponent(fileKey, networkNumber, compNumber, mainComponent["AuxContent"].toString());
        if (newComNumber <= 0)
        {
            LOG_ERROR_DEFAULT("插入元件线圈/跳转/返回/Set1/Set0失败");
            return false;
        }
    }
    QSharedPointer<LDComponent> newCoilComponent = ld->searchComponentFromNumber(newComNumber);
    // 获取传入数据赋值给newCom
    QJsonObject newComJson = componentData["components"].toArray()[0].toObject();

    // 保存原始编号用于后续映射更新
    int originalNewComNumber = newCoilComponent->Number;
    int targetNewComNumber = newComJson["Number"].toInt();

    newCoilComponent->Type = newComJson["Type"].toString();
    newCoilComponent->ChildType = newComJson["ChildType"].toString();
    newCoilComponent->AuxContent = newComJson["AuxContent"].toString();
    newCoilComponent->InstanceName = newComJson["InstanceName"].toString();
    newCoilComponent->Comment = newComJson["Comment"].toString();
    newCoilComponent->Name = newComJson["Name"].toString();
    newCoilComponent->DataType_Local = newComJson["DataType_Local"].toString();
    newCoilComponent->Source = newComJson["Source"].toString();
    newCoilComponent->Comment = newComJson["Comment"].toString();
    newCoilComponent->SupportsInPinType = newComJson["SupportsInPinType"].toString();
    newCoilComponent->SupportsOutPinType = newComJson["SupportsOutPinType"].toString();
    newCoilComponent->SupportsInPinAdd = newComJson["SupportsInPinAdd"].toBool();
    newCoilComponent->SupportsOutPinAdd = newComJson["SupportsOutPinAdd"].toBool();
    newCoilComponent->Width = newComJson["Width"].toInt();
    newCoilComponent->Height = newComJson["Height"].toInt();

    LOG_INFO_DEFAULT("粘贴线圈/跳转/返回到元件{}", compNumber);

    // 获取传入数据中的连接信息
    QJsonArray connectionsArray = componentData["connections"].toArray();

    // 创建元件编号映射表（原始编号 -> 新编号）
    QMap<int, int> componentNumberMap;
    int originalMainNumber = componentData["mainComponentNumber"].toInt();
    componentNumberMap[originalMainNumber] = newCoilComponent->Number;

    // 创建并连接变量元件（如果传入数据中有相关变量）
    QJsonArray componentsArray = componentData["components"].toArray();
    for (int i = 1; i < componentsArray.size(); ++i)
    {
        QJsonObject varComponentJson = componentsArray[i].toObject();
        if (varComponentJson["Type"].toString() == "Variable")
        {
            QSharedPointer<LDComponent> varComponent =
                createComponentFromClipboard(varComponentJson, fileKey, networkNumber);
            if (varComponent)
            {
                network->componentMap.insert(varComponent->Number, varComponent);
                ld->components->componentMap.insert(varComponent->Number, varComponent);

                // 记录原始编号到新编号的映射
                int originalVarNumber = varComponentJson["Number"].toInt();
                componentNumberMap[originalVarNumber] = varComponent->Number;
            }
        }
    }

    // 恢复原始的连接关系，保持PinId顺序一致
    for (const QJsonValue &connectionValue : connectionsArray)
    {
        QJsonObject connectionJson = connectionValue.toObject();
        int originalSourceNumber = connectionJson["SourceComponentNumber"].toInt();
        int originalTargetNumber = connectionJson["TargetComponentNumber"].toInt();
        int sourcePinId = connectionJson["SourcePinId"].toInt();
        int targetPinId = connectionJson["TargetPinId"].toInt();
        int sourceConnectIndex = connectionJson["SourceConnectIndex"].toInt();
        int targetConnectIndex = connectionJson["TargetConnectIndex"].toInt();

        // 检查连接是否涉及变量元件和线圈元件
        if (componentNumberMap.contains(originalSourceNumber) && componentNumberMap.contains(originalTargetNumber))
        {
            int newSourceNumber = componentNumberMap[originalSourceNumber];
            int newTargetNumber = componentNumberMap[originalTargetNumber];

            // 确保引脚存在（使用原始的PinId）
            QSharedPointer<LDComponent> sourceComp = ld->searchComponentFromNumber(newSourceNumber);
            QSharedPointer<LDComponent> targetComp = ld->searchComponentFromNumber(newTargetNumber);

            if (sourceComp && targetComp)
            {
                // 根据JSON中的connectors信息查找或创建源引脚
                QSharedPointer<LDConnector> sourceConnector =
                    findOrCreateConnectorFromJson(sourceComp, sourcePinId, componentsArray);

                // 根据JSON中的connectors信息查找或创建目标引脚
                QSharedPointer<LDConnector> targetConnector =
                    findOrCreateConnectorFromJson(targetComp, targetPinId, componentsArray);

                // 建立连接，保持原始的PinId
                QSharedPointer<LDConnection> newConnection =
                    addConnection(fileKey, newSourceNumber, sourcePinId, sourceConnectIndex, newTargetNumber,
                                  targetPinId, targetConnectIndex, true);
                if (nullptr != newConnection)
                {
                    // 设置父子关系（如果是变量元件连接到线圈元件）
                    if (sourceComp->Type == "Variable" && targetComp->Type == "Block")
                    {
                        sourceComp->ParentNumber = newTargetNumber;
                        sourceComp->ParentPinId = sourcePinId;
                        sourceComp->ParentPinDataType = "BOOL";
                    }

                    LOG_INFO_DEFAULT("成功恢复线圈元件连接: {} (PinId:{}) -> {} (PinId:{})", newSourceNumber,
                                     sourcePinId, newTargetNumber, targetPinId);
                }
                else
                {
                    LOG_ERROR_DEFAULT("恢复线圈元件连接失败: {} -> {}", newSourceNumber, newTargetNumber);
                }
            }
        }
    }

    LOG_INFO_DEFAULT("成功粘贴线圈/跳转/返回到元件{}，保持原始PinId连接顺序", compNumber);
    return true;
}

// 粘贴块/扩展块
bool LDManager::pasteBlockComponent(const QString &fileKey, int compNumber, const QJsonObject &componentData,
                                    int leftOrRight)
{
    LOG_INFO_DEFAULT("粘贴块/扩展块");

    // 检查元件数据是否有效
    if (componentData.isEmpty())
    {
        LOG_ERROR_DEFAULT("元件数据为空，无法粘贴");
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();
    QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
    int networkNumber = com_ptr->NetworkNumber;
    QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);

    if (!com_ptr)
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return false;
    }
    int newComNumber = addBlockComponent(fileKey, networkNumber, compNumber,
                                         componentData["components"].toArray()[0].toObject(), leftOrRight);
    QSharedPointer<LDComponent> newBlockComponent = ld->searchComponentFromNumber(newComNumber);
    if (!newBlockComponent)
    {
        LOG_ERROR_DEFAULT("插入元件并联OR块失败");
        return false;
    }

    // 获取传入数据赋值给newCom
    QJsonObject newComJson = componentData["components"].toArray()[0].toObject();

    // 保存原始编号用于后续映射更新
    int originalNewComNumber = newBlockComponent->Number;
    int targetNewComNumber = newComJson["Number"].toInt();

    newBlockComponent->Type = newComJson["Type"].toString();
    newBlockComponent->ChildType = newComJson["ChildType"].toString();
    newBlockComponent->AuxContent = newComJson["AuxContent"].toString();
    newBlockComponent->InstanceName = newComJson["InstanceName"].toString();
    newBlockComponent->Comment = newComJson["Comment"].toString();
    newBlockComponent->Name = newComJson["Name"].toString();
    newBlockComponent->DataType_Local = newComJson["DataType_Local"].toString();
    newBlockComponent->Source = newComJson["Source"].toString();
    newBlockComponent->Comment = newComJson["Comment"].toString();
    newBlockComponent->SupportsInPinType = newComJson["SupportsInPinType"].toString();
    newBlockComponent->SupportsOutPinType = newComJson["SupportsOutPinType"].toString();
    newBlockComponent->SupportsInPinAdd = newComJson["SupportsInPinAdd"].toBool();
    newBlockComponent->SupportsOutPinAdd = newComJson["SupportsOutPinAdd"].toBool();
    newBlockComponent->Width = newComJson["Width"].toInt();
    newBlockComponent->Height = newComJson["Height"].toInt();

    // 获取传入数据中的连接信息
    QJsonArray connectionsArray = componentData["connections"].toArray();

    // 创建元件编号映射表（原始编号 -> 新编号）
    QMap<int, int> componentNumberMap;
    int originalMainNumber = componentData["mainComponentNumber"].toInt();
    componentNumberMap[originalMainNumber] = newBlockComponent->Number;

    // 创建并连接变量元件（如果传入数据中有相关变量）
    QJsonArray componentsArray = componentData["components"].toArray();
    for (int i = 1; i < componentsArray.size(); ++i)
    {
        QJsonObject varComponentJson = componentsArray[i].toObject();
        if (varComponentJson["Type"].toString() == "Variable" && varComponentJson["ChildType"].toString() == "Local" &&
            varComponentJson["AuxContent"].toString() == "???")
        {
            QSharedPointer<LDComponent> varComponent =
                createComponentFromClipboard(varComponentJson, fileKey, networkNumber);
            if (varComponent)
            {
                network->componentMap.insert(varComponent->Number, varComponent);
                ld->components->componentMap.insert(varComponent->Number, varComponent);

                // 记录原始编号到新编号的映射
                int originalVarNumber = varComponentJson["Number"].toInt();
                componentNumberMap[originalVarNumber] = varComponent->Number;
            }
        }
    }

    // 恢复原始的连接关系，保持PinId顺序一致
    for (const QJsonValue &connectionValue : connectionsArray)
    {
        QJsonObject connectionJson = connectionValue.toObject();
        int originalSourceNumber = connectionJson["SourceComponentNumber"].toInt();
        int originalTargetNumber = connectionJson["TargetComponentNumber"].toInt();
        int sourcePinId = connectionJson["SourcePinId"].toInt();
        int targetPinId = connectionJson["TargetPinId"].toInt();
        int sourceConnectIndex = connectionJson["SourceConnectIndex"].toInt();
        int targetConnectIndex = connectionJson["TargetConnectIndex"].toInt();

        // 检查连接是否涉及变量元件和块元件
        if (componentNumberMap.contains(originalSourceNumber) && componentNumberMap.contains(originalTargetNumber))
        {
            int newSourceNumber = componentNumberMap[originalSourceNumber];
            int newTargetNumber = componentNumberMap[originalTargetNumber];

            // 确保引脚存在（使用原始的PinId）
            QSharedPointer<LDComponent> sourceComp = ld->searchComponentFromNumber(newSourceNumber);
            QSharedPointer<LDComponent> targetComp = ld->searchComponentFromNumber(newTargetNumber);

            if (sourceComp && targetComp)
            {
                // 根据JSON中的connectors信息查找或创建源引脚
                QSharedPointer<LDConnector> sourceConnector =
                    findOrCreateConnectorFromJson(sourceComp, sourcePinId, componentsArray);

                // 根据JSON中的connectors信息查找或创建目标引脚
                QSharedPointer<LDConnector> targetConnector =
                    findOrCreateConnectorFromJson(targetComp, targetPinId, componentsArray);

                // 建立连接，保持原始的PinId
                QSharedPointer<LDConnection> newConnection =
                    addConnection(fileKey, newSourceNumber, sourcePinId, sourceConnectIndex, newTargetNumber,
                                  targetPinId, targetConnectIndex, true);
                if (nullptr != newConnection)
                {
                    // 设置父子关系（如果是变量元件连接到块元件）
                    if (sourceComp->Type == "Variable" && targetComp->Type == "Block")
                    {
                        sourceComp->ParentNumber = newTargetNumber;
                        sourceComp->ParentPinId = sourcePinId;
                        sourceComp->ParentPinDataType = "BOOL";
                    }

                    LOG_INFO_DEFAULT("成功恢复块元件连接: {} (PinId:{}) -> {} (PinId:{})", newSourceNumber, sourcePinId,
                                     newTargetNumber, targetPinId);
                }
                else
                {
                    LOG_ERROR_DEFAULT("恢复块元件连接失败: {} -> {}", newSourceNumber, newTargetNumber);
                }
            }
        }
    }

    LOG_INFO_DEFAULT("成功粘贴块元件到元件{}，保持原始PinId连接顺序", compNumber);
    return true;
}

// 在元件左侧添加水平串联触点
QSharedPointer<LDComponent> LDManager::addSeriesContactToLeft(const QString &fileKey,
                                                              QSharedPointer<LDComponent> com_ptr,
                                                              const QString compType)
{
    // 获取插入位置元件的IN引脚
    QSharedPointer<LDConnector> inConnector = getFirstINConnector(fileKey, com_ptr->Number);
    if (!inConnector)
    {
        LOG_ERROR_DEFAULT("获取插入位置元件IN引脚失败");
        return nullptr;
    }

    // 获取IN引脚的连接
    QSharedPointer<LDConnection> existingConnection =
        getInputConnectorLDConnection(fileKey, com_ptr->Number, inConnector->PinId);
    if (!existingConnection)
    {
        LOG_ERROR_DEFAULT("获取插入位置元件IN引脚的连接失败");
        return nullptr;
    }
    LOG_INFO_DEFAULT("元件{}索引{}->元件{}索引{}", existingConnection->SourceComponentNumber,
                     existingConnection->SourceConnectIndex, existingConnection->TargetComponentNumber,
                     existingConnection->TargetConnectIndex);
    // 构造AND块（触点）
    QSharedPointer<LDComponent> andCom = createAndBlock(fileKey, com_ptr);
    if (!andCom)
    {
        LOG_ERROR_DEFAULT("构造AND块失败");
        return nullptr;
    }

    // AND块添加IN引脚
    int andInPinId = addINConnector(fileKey, andCom->Number);
    LOG_INFO_DEFAULT("元件{} 添加IN引脚 ", andCom->Number);
    if (andInPinId == 0)
    {
        LOG_ERROR_DEFAULT("AND块添加IN引脚失败");
        return nullptr;
    }

    // 修改现有连接目标为 -> 新添加的AND块
    existingConnection->TargetComponentNumber = andCom->Number;
    existingConnection->TargetPinId = andInPinId;
    existingConnection->TargetConnectIndex = 0;

    LOG_INFO_DEFAULT("元件{}索引{}->元件{}索引{}", existingConnection->SourceComponentNumber,
                     existingConnection->SourceConnectIndex, existingConnection->TargetComponentNumber,
                     existingConnection->TargetConnectIndex);
    // AND块添加OUT引脚
    QSharedPointer<LDConnector> andOutConnector = getFirstOUTConnector(fileKey, andCom->Number);
    if (!andOutConnector)
    {
        LOG_ERROR_DEFAULT("获取AND块OUT引脚失败");
        return nullptr;
    }

    // AND块的OUT引脚 -> 插入位置元件的IN引脚
    QSharedPointer<LDConnection> newConnection =
        addConnection(fileKey, andCom->Number, andOutConnector->PinId, 0, com_ptr->Number, inConnector->PinId, 0, true);
    if (nullptr == newConnection)
    {
        LOG_ERROR_DEFAULT("AND块OUT引脚 -> 插入位置元件IN引脚连接失败");
        return nullptr;
    }
    return andCom;
}

// 在元件右侧添加水平串联元件 触点/OR块
QSharedPointer<LDComponent> LDManager::addSeriesContactToRight(const QString &fileKey,
                                                               QSharedPointer<LDComponent> com_ptr,
                                                               const QString compType)
{
    // 检查文件是否存在
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在");
        return nullptr;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    if (!com_ptr)
    {
        LOG_ERROR_DEFAULT("元件不存在");
        return nullptr;
    }

    // 构造元件
    QSharedPointer<LDComponent> newCom = createBlock(fileKey, com_ptr, compType);
    if (!newCom)
    {
        LOG_ERROR_DEFAULT("构造元件失败");
        return nullptr;
    }

    // 收集插入元件输出的所有链接
    QList<QSharedPointer<LDConnection>> outConnections;

    // 遍历所有链接
    for (auto &connection : ld->connections->connectionList)
    {
        if (connection->SourceComponentNumber == com_ptr->Number)
        {
            outConnections.append(connection);
        }
        // 按照connection->SourceConnectIndex从大到小排序
        std::sort(outConnections.begin(), outConnections.end(),
                  [](const QSharedPointer<LDConnection> &a, const QSharedPointer<LDConnection> &b) {
                      return a->SourceConnectIndex > b->SourceConnectIndex;
                  });
    }

    for (auto &connection : outConnections)
    {
        // 修改连接源为插入元件
        connection->SourceComponentNumber = newCom->Number;
        QSharedPointer<LDConnector> newOutConnector = getFirstOUTConnector(fileKey, newCom->Number);
        if (!newOutConnector)
        {
            LOG_ERROR_DEFAULT("获取新元件OUT引脚失败");
            return nullptr;
        }
    }

    // 元件添加IN引脚
    QSharedPointer<LDConnector> newInConnector = insertINConnector(fileKey, newCom->Number, 1);
    LOG_INFO_DEFAULT("{}元件插入IN引脚{}", newCom->Number, newInConnector->PinId);
    if (newInConnector->PinId != 2)
    {
        LOG_ERROR_DEFAULT("元件{}的IN引脚PinId{}错误", newCom->Number, newInConnector->PinId);
        return nullptr;
    }
    // 获取插入位置的第一个OUT引脚
    QSharedPointer<LDConnector> outConnector = getFirstOUTConnector(fileKey, com_ptr->Number);
    if (!outConnector)
    {
        LOG_ERROR_DEFAULT("获取插入位置元件的OUT引脚失败");
        return nullptr;
    }

    // 调整插入位置元件的OUT引脚连接索引
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        if (connection->SourceComponentNumber == com_ptr->Number)
        {
            if (connection->SourceConnectIndex >= 0)
            {
                connection->SourceConnectIndex++;
            }
        }
    }

    // 水平串联连接：插入位置元件OUT引脚 -> 新元件IN引脚
    QSharedPointer<LDConnection> newConnection =
        addConnection(fileKey, com_ptr->Number, outConnector->PinId, 0, newCom->Number, newInConnector->PinId, 0, true);
    if (nullptr == newConnection)
    {
        LOG_ERROR_DEFAULT("插入位置元件{}的OUT引脚 -> 新元件{}的IN引脚连接失败", com_ptr->Number, newCom->Number);
        return nullptr;
    }
    LOG_INFO_DEFAULT("{}元件的{}->{}元件的{}连接成功", com_ptr->Number, newConnection->SourceConnectIndex,
                     newCom->Number, newConnection->TargetConnectIndex);
    return newCom;
}

// 并联触点
int LDManager::addParallelContact(const QString &fileKey, int compNumber)
{
    LOG_INFO_DEFAULT("选中元件{}并联触点", compNumber);

    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return -1;
    }
    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 检查元件是否存在
    QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
    if (!com_ptr)
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return -1;
    }

    // 根据NetworkNumber判断该元件所属网络是否存在除起始常量元件外的其他元件
    if (ld->networks->networkMap.value(com_ptr->NetworkNumber)->componentMap.size() == 1)
    {
        // 如果网络内只有一个起始常量元件 不可进行并联
        LOG_ERROR_DEFAULT("网络内只有一个起始常量元件，不可进行并联: networkNumber={}", com_ptr->NetworkNumber);
        return -1;
    }

    // 获取插入位置元件的第一个IN引脚
    QSharedPointer<LDConnector> inConnector = getFirstINConnector(fileKey, compNumber);
    if (!inConnector)
    {
        LOG_ERROR_DEFAULT("获取插入位置元件IN引脚失败");
        return -1;
    }

    // 获取与插入位置元件第一个IN引脚的连接
    QSharedPointer<LDConnection> sourceConnection =
        getInputConnectorLDConnection(fileKey, compNumber, inConnector->PinId);
    if (!sourceConnection)
    {
        LOG_ERROR_DEFAULT("获取插入位置元件IN引脚的连接失败");
        return -1;
    }

    // 根据IN引脚的连接获取插入位置元件的来源元件
    QSharedPointer<LDComponent> sourceCom = ld->searchComponentFromNumber(sourceConnection->SourceComponentNumber);
    if (!sourceCom)
    {
        LOG_ERROR_DEFAULT("获取插入位置元件的来源元件失败");
        return -1;
    }

    // 获取来源元件的第一个OUT引脚
    QSharedPointer<LDConnector> newOutConnector = getFirstOUTConnector(fileKey, sourceCom->Number);
    if (!newOutConnector)
    {
        LOG_ERROR_DEFAULT("插入来源元件OUT引脚失败");
        return -1;
    }

    // 创建新的AND块(并联触点)
    QSharedPointer<LDComponent> newAndBlock = createAndBlock(fileKey, com_ptr);
    if (!newAndBlock)
    {
        LOG_ERROR_DEFAULT("创建并联AND块失败");
        return -1;
    }

    // 为新AND块添加输入引脚
    int newAndInPinId = addINConnector(fileKey, newAndBlock->Number);
    if (newAndInPinId == 0)
    {
        LOG_ERROR_DEFAULT("新AND块添加IN引脚失败");
        return -1;
    }
    // 获取选中元件输入连接的索引作为基准
    int baseSourceIndex = sourceConnection->SourceConnectIndex + 1;
    int baseTargetIndex = getMaxTargetConnectIndex(fileKey, newAndBlock->Number) + 1;
    // 先调整现有连接索引,为新连接腾出空间,调整所有受影响的现有以sourceCom->Number为源的连接索引
    adjustExistingConnectionIndexes(fileKey, sourceCom->Number, newAndBlock->Number, baseSourceIndex, baseTargetIndex);
    // 源元件新插入的OUT引脚 -> 新AND块IN引脚
    QSharedPointer<LDConnection> newConnection =
        addConnection(fileKey, sourceCom->Number, newOutConnector->PinId, baseSourceIndex, newAndBlock->Number,
                      newAndInPinId, baseTargetIndex, true);
    if (nullptr == newConnection)
    {
        LOG_ERROR_DEFAULT("设置元件{}的引脚{}->元件{}的引脚{}连接失败: SourceConnectIndex={}, TargetConnectIndex={}",
                          sourceCom->Number, newOutConnector->PinId, newAndBlock->Number, newAndInPinId,
                          baseSourceIndex, baseTargetIndex);

        return -1;
    }

    LOG_INFO_DEFAULT("设置元件{}的{}->元件{}的{}连接成功: SourceConnectIndex={}, TargetConnectIndex={}",
                     sourceCom->Number, newConnection->SourcePinId, newAndBlock->Number, newConnection->TargetPinId,
                     newConnection->SourceConnectIndex, newConnection->TargetConnectIndex);
    // 获取插入位置元件的第一个输出引脚
    QSharedPointer<LDConnector> outConnector = getFirstOUTConnector(fileKey, compNumber);
    if (!outConnector)
    {
        LOG_ERROR_DEFAULT("获取插入位置元件的第一个OUT引脚失败");
        return -1;
    }

    // 获取插入位置元件的第一个输出引脚的连接
    QSharedPointer<LDConnection> outConnection =
        getOutputConnectorLDConnection(fileKey, compNumber, outConnector->PinId);
    QSharedPointer<LDComponent> targetComponent = nullptr;
    if (outConnection)
    {
        targetComponent = ld->searchComponentFromNumber(outConnection->TargetComponentNumber);
    }

    // 为新AND块添加一个输出引脚
    int newAndOutPinId = addOUTConnector(fileKey, newAndBlock->Number);
    if (newAndOutPinId == 0)
    {
        LOG_ERROR_DEFAULT("新AND块添加OUT引脚失败");
        return -1;
    }

    // 创建一个变量块连接到新AND块
    if (!connectVariableToComponent(fileKey, newAndBlock))
    {
        LOG_ERROR_DEFAULT("连接变量块到新AND块失败");
        return -1;
    }
    // LOG_INFO_DEFAULT("连接变量块到新AND块成功");
    // 查找是否插入位置元件已连接到OR块
    if (outConnection && targetComponent->Type == "Block" && targetComponent->ChildType == "Func" &&
        targetComponent->AuxContent == "Or")
    {
        // 如果当前元件已经连接到一个OR块，使用这个OR块
        // 在添加连接之前获取OR块的最大TargetConnectIndex
        int maxTargetIndexBeforeAdd = getMaxTargetConnectIndex(fileKey, targetComponent->Number);

        // 调整orConnection->TargetConnectIndex后续的连接索引
        adjustORBlockInputIndexes(fileKey, targetComponent->Number, outConnection->TargetConnectIndex + 1);
        // OR块插入IN引脚
        QSharedPointer<LDConnector> newInConnector =
            insertINConnector(fileKey, targetComponent->Number, outConnection->TargetPinId);
        if (!newInConnector)
        {
            LOG_ERROR_DEFAULT("插入OR块IN引脚失败");
            return -1;
        }
        // 新AND块OUT引脚 -> OR块IN引脚
        QSharedPointer<LDConnection> newConnection =
            addConnection(fileKey, newAndBlock->Number, newAndOutPinId, 0, targetComponent->Number,
                          newInConnector->PinId, outConnection->TargetConnectIndex + 1, true);
        if (nullptr == newConnection)
        {
            LOG_ERROR_DEFAULT("元件{}的{}->元件{}的{}连接失败", newAndBlock->Number, 0, targetComponent->Number, 0);
            return -1;
        }
        LOG_INFO_DEFAULT("设置元件{}的{}->元件{}的{}连接成功", newAndBlock->Number, newConnection->SourceConnectIndex,
                         targetComponent->Number, newConnection->TargetConnectIndex);
    }
    else
    {
        // 如果不存在OR块，则插入元件串联OR块
        // 插入元件水平串联OR块
        QSharedPointer<LDComponent> orBlock = addSeriesContactToRight(fileKey, com_ptr, "Or");
        if (!orBlock)
        {
            LOG_ERROR_DEFAULT("插入元件串联OR块失败");
            return -1;
        }
        int orInPinId = addINConnector(fileKey, orBlock->Number);
        if (orInPinId == 0)
        {
            LOG_ERROR_DEFAULT("插入元件串联OR块失败");
            return -1;
        }
        // 新AND块OUT引脚 -> 新OR块IN引脚
        int sourceConnectIndex = getMaxSourceConnectIndex(fileKey, newAndBlock->Number) + 1;
        int targetConnectIndex = getMaxTargetConnectIndex(fileKey, orBlock->Number) + 1;
        QSharedPointer<LDConnection> newConnection =
            addConnection(fileKey, newAndBlock->Number, newAndOutPinId, sourceConnectIndex, orBlock->Number, orInPinId,
                          targetConnectIndex, true);
        if (nullptr == newConnection)
        {
            LOG_ERROR_DEFAULT("元件{}的引脚{}->元件{}的引脚{}连接失败: SourceConnectIndex={}, TargetConnectIndex={}",
                              newAndBlock->Number, newAndOutPinId, orBlock->Number, orInPinId, sourceConnectIndex,
                              targetConnectIndex);
            return -1;
        }
    }

    return compNumber;
}

// 删除触点
bool LDManager::deleteContact(const QString &fileKey, int compNumber)
{
    LOG_INFO_DEFAULT("删除触点 compNumber={}", compNumber);

    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 检查元件是否存在
    if (!ld->components->componentMap.contains(compNumber))
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return false;
    }

    QSharedPointer<LDComponent> targetComponent = ld->searchComponentFromNumber(compNumber);

    // 1. 收集需要删除的元件和连接
    QList<int> componentsToDelete;
    QList<QSharedPointer<LDConnection>> connectionsToDelete;

    // 添加元件到删除列表
    componentsToDelete.append(compNumber);

    // 查找与元件关联的变量元件
    for (auto &component : ld->components->componentMap)
    {
        if (component->ParentNumber == compNumber && component->Type == "Variable")
        {
            componentsToDelete.append(component->Number);
            LOG_INFO_DEFAULT("找到关联的变量元件: {}", component->Number);
        }
    }

    // 2. 分析连接结构，判断是串联还是并联
    QSharedPointer<LDConnection> inputConnection = nullptr;       // 被删除元件的水平输入连接
    QList<QSharedPointer<LDConnection>> outputConnectionList;     // 被删除元件的输出连接
    QSharedPointer<LDComponent> sourceComponent = nullptr;        // 被删除元件的输入源元件
    QList<QSharedPointer<LDComponent>> outputTargetComponentList; // 被删除元件的输出目标元件
    bool isParallel = false;

    // 查找被删除元件的水平输入连接
    for (auto &connection : ld->connections->connectionList)
    {
        if (connection->TargetComponentNumber == compNumber && connection->TargetConnectIndex == 0 &&
            connection->TargetDataType != "Variable")
        {
            QSharedPointer<LDComponent> srcComp = ld->searchComponentFromNumber(connection->SourceComponentNumber);
            inputConnection = connection;
            sourceComponent = srcComp;
            LOG_INFO_DEFAULT("找到输入连接: 源元件{}的索引{} -> 目标元件{}的索引{}", connection->SourceComponentNumber,
                             connection->SourceConnectIndex, connection->TargetComponentNumber,
                             connection->TargetConnectIndex);
        }
    }

    // 查找被删除元件的输出连接
    for (auto &connection : ld->connections->connectionList)
    {
        if (connection->SourceComponentNumber == compNumber && connection->TargetDataType != "Variable")
        {
            outputConnectionList.append(connection);

            QSharedPointer<LDComponent> outputTargetComponent =
                ld->searchComponentFromNumber(connection->TargetComponentNumber);
            outputTargetComponentList.append(outputTargetComponent);
            LOG_INFO_DEFAULT("找到输出连接: 源元件{}的索引{} -> 目标元件{}的索引{}", connection->SourceComponentNumber,
                             connection->SourceConnectIndex, connection->TargetComponentNumber,
                             connection->TargetConnectIndex);

            // 判断是否输出目标元件为OR块
            if (outputTargetComponent->Type == "Block" && outputTargetComponent->ChildType == "Func" &&
                outputTargetComponent->AuxContent == "Or")
            {
                isParallel = true;
                LOG_INFO_DEFAULT("检测到并联结构，目标为OR块: {}", outputTargetComponent->Number);
            }
        }
    }
    // 3. 处理并联触点的特殊情况(删除元件的输出目标元件是OR块)
    if (isParallel)
    {
        QSharedPointer<LDComponent> outputTargetComponent = outputTargetComponentList.first();
        // 检查OR块还有多少输入
        int orInputCount = 0;
        for (auto &connection : ld->connections->connectionList)
        {
            if (connection->TargetComponentNumber == outputTargetComponent->Number && connection->TargetPinId > 1)
            {
                orInputCount++;
            }
        }

        LOG_INFO_DEFAULT("OR块{}有{}个输入", outputTargetComponent->Number, orInputCount);

        // 如果删除元件的输出目标元件是OR块OR块只有2个输入
        if (orInputCount <= 2)
        {
            // 找到OR块的另一个输入链接
            QSharedPointer<LDConnection> otherInputConnection = nullptr;
            // 找到OR块的另一个输入
            QSharedPointer<LDComponent> otherInputComponent = nullptr;
            for (auto &connection : ld->connections->connectionList)
            {
                if (connection->TargetComponentNumber == outputTargetComponent->Number && connection->TargetPinId > 1 &&
                    connection->SourceComponentNumber != compNumber)
                {
                    otherInputConnection = connection;
                    if (ld->components->componentMap.contains(connection->SourceComponentNumber))
                    {
                        otherInputComponent = ld->searchComponentFromNumber(connection->SourceComponentNumber);
                        LOG_INFO_DEFAULT("找到OR块的另一个输入: 元件={}, 引脚={}", connection->SourceComponentNumber,
                                         connection->SourcePinId);
                    }
                    break;
                }
            }
            // 判断被删除元件的输入链接是否是水平串联，且被删除元件的输入元件到OR块的另一个输入元件之间不存在连接,保留OR块
            if (inputConnection->SourceConnectIndex == 0 && inputConnection->TargetConnectIndex == 0 &&
                !getConnection(fileKey, inputConnection->SourceComponentNumber, otherInputComponent->Number))
            {
                // 收集需要删除的连接
                for (auto &connection : ld->connections->connectionList)
                {
                    // 删除与要删除的AND块相关的连接
                    if (connection->SourceComponentNumber == compNumber ||
                        connection->TargetComponentNumber == compNumber ||
                        componentsToDelete.contains(connection->SourceComponentNumber) ||
                        componentsToDelete.contains(connection->TargetComponentNumber))
                    {
                        connectionsToDelete.append(connection);
                    }
                }
                // 先删除所有连接，释放连接索引
                for (auto &connection : connectionsToDelete)
                {
                    ld->connections->connectionList.removeOne(connection);
                }
                // 然后重建连接
                QSharedPointer<LDConnection> newConnection = addConnection(
                    fileKey, inputConnection->SourceComponentNumber, inputConnection->SourcePinId,
                    inputConnection->SourceConnectIndex, outputTargetComponent->Number,
                    outputConnectionList.first()->TargetPinId, outputConnectionList.first()->TargetConnectIndex, true);
                if (nullptr == newConnection)
                {
                    LOG_ERROR_DEFAULT("建立连接失败: {}的引脚{} -> {}的引脚{}", inputConnection->SourceComponentNumber,
                                      inputConnection->SourcePinId, outputTargetComponent->Number,
                                      outputConnectionList.first()->TargetPinId);
                }
                else
                {
                    LOG_INFO_DEFAULT("建立连接成功: {}的引脚{} -> {}的引脚{}", inputConnection->SourceComponentNumber,
                                     inputConnection->SourcePinId, outputTargetComponent->Number,
                                     outputConnectionList.first()->TargetPinId);
                }
            }
            else // OR块只有2个输入且不存在水平串联的输入链接则需要删除OR块
            {
                componentsToDelete.append(outputTargetComponent->Number);
                LOG_INFO_DEFAULT("OR块只有2个输入，且删除的元件没有水平串联的前驱元件触点，删除OR块");
                // 存储所有以OR块为源的连接
                QList<QSharedPointer<LDConnection>> orOutputConnections;
                // 存储所有以OR块为源的连接
                for (auto &connection : ld->connections->connectionList)
                {
                    if (connection->SourceComponentNumber == outputTargetComponent->Number)
                    {
                        orOutputConnections.append(connection);
                        LOG_INFO_DEFAULT("找到OR块的输出:源OR块{}的索引{} -> 目标元件{}的索引{}",
                                         connection->SourceComponentNumber, connection->SourceConnectIndex,
                                         connection->TargetComponentNumber, connection->TargetConnectIndex);
                    }
                }

                // 收集所有需要删除的连接
                for (auto &connection : ld->connections->connectionList)
                {
                    // 删除与要删除的元件相关的所有连接
                    for (int compId : componentsToDelete)
                    {
                        if (connection->SourceComponentNumber == compId || connection->TargetComponentNumber == compId)
                        {
                            connectionsToDelete.append(connection);
                            LOG_INFO_DEFAULT("删除连接:{}->{}", connection->SourceComponentNumber,
                                             connection->TargetComponentNumber);
                        }
                    }
                }

                // 删除所有连接
                for (auto &connection : connectionsToDelete)
                {
                    ld->connections->connectionList.removeOne(connection);
                    deleteExtraOutConnectors(fileKey, connection->SourceComponentNumber);
                    deleteExtraOutConnectors(fileKey, connection->TargetComponentNumber);
                }
                // 如果有另一个输入和以OR块为源的输出元件建立直接连接
                if (otherInputComponent && !orOutputConnections.isEmpty())
                {
                    // 从orOutputConnections遍历时，应该按照orOutputConnection->SourcePinId 从大到小遍历
                    // 对orOutputConnections进行排序，按SourcePinId从大到小排序
                    std::sort(orOutputConnections.begin(), orOutputConnections.end(),
                              [](const QSharedPointer<LDConnection> &a, const QSharedPointer<LDConnection> &b) {
                                  return a->SourcePinId > b->SourcePinId;
                              });

                    for (auto &orOutputConnection : orOutputConnections)
                    {

                        int targetCompNum = orOutputConnection->TargetComponentNumber;
                        int targetPinId = orOutputConnection->TargetPinId;
                        int SourcePinId = addOUTConnector(fileKey, otherInputComponent->Number);
                        QSharedPointer<LDConnection> newConnection = addConnection(
                            fileKey, otherInputComponent->Number, SourcePinId, orOutputConnection->TargetConnectIndex,
                            targetCompNum, targetPinId, orOutputConnection->TargetConnectIndex, true);
                        if (nullptr == newConnection)
                        {
                            LOG_ERROR_DEFAULT("建立直接连接失败");
                        }
                        else
                        {
                            LOG_INFO_DEFAULT("建立直接连接: {}的引脚{} -> {}的引脚{}", otherInputComponent->Number,
                                             SourcePinId, targetCompNum, targetPinId);
                        }
                    }
                }
            }
        }
        else // 如果删除元件的输出目标元件是OR块, 且OR块有大于2个输入
        {
            // 存在水平串联的输入链接，
            QSharedPointer<LDComponent> srcComp = ld->searchComponentFromNumber(inputConnection->SourceComponentNumber);
            // 判断该输出元件是否存在其他输出连接
            bool hasOtherOutputConnection = false;
            for (auto &connection : ld->connections->connectionList)
            {
                if (connection->SourceComponentNumber == srcComp->Number &&
                    connection->TargetComponentNumber != compNumber)
                {
                    hasOtherOutputConnection = true;
                }
            }
            // 如果删除元件的输入链接是水平串联，且被删除元件的输入元件到OR块的另一个输入元件之间不存在连接,保留OR块
            if (inputConnection->SourceConnectIndex == 0 && inputConnection->TargetConnectIndex == 0 &&
                !hasOtherOutputConnection)
            {
                // 收集需要删除的连接
                for (auto &connection : ld->connections->connectionList)
                {
                    // 删除与要删除的AND块相关的连接
                    if (connection->SourceComponentNumber == compNumber ||
                        connection->TargetComponentNumber == compNumber ||
                        componentsToDelete.contains(connection->SourceComponentNumber) ||
                        componentsToDelete.contains(connection->TargetComponentNumber))
                    {
                        connectionsToDelete.append(connection);
                    }
                }
                // 先删除所有连接，释放连接索引
                for (auto &connection : connectionsToDelete)
                {
                    ld->connections->connectionList.removeOne(connection);
                }
                LOG_INFO_DEFAULT("存在水平串联的输入链接，不删除OR块{}", outputTargetComponent->Number);
                // 然后重建连接
                QSharedPointer<LDConnection> newConnection = addConnection(
                    fileKey, inputConnection->SourceComponentNumber, inputConnection->SourcePinId,
                    inputConnection->SourceConnectIndex, outputTargetComponent->Number,
                    outputConnectionList.first()->TargetPinId, outputConnectionList.first()->TargetConnectIndex, true);
                if (nullptr == newConnection)
                {
                    LOG_ERROR_DEFAULT("建立连接失败: {}的引脚{} -> {}的引脚{}", inputConnection->SourceComponentNumber,
                                      inputConnection->SourcePinId, outputTargetComponent->Number,
                                      outputConnectionList.first()->TargetPinId);
                }
                else
                {
                    LOG_INFO_DEFAULT("建立连接成功: {}的引脚{} -> {}的引脚{}", inputConnection->SourceComponentNumber,
                                     inputConnection->SourcePinId, outputTargetComponent->Number,
                                     outputConnectionList.first()->TargetPinId);
                }
            }
            else
            {
                // 收集需要删除的连接
                for (auto &connection : ld->connections->connectionList)
                {
                    // 删除与要删除的AND块相关的连接
                    if (connection->SourceComponentNumber == compNumber ||
                        connection->TargetComponentNumber == compNumber ||
                        componentsToDelete.contains(connection->SourceComponentNumber) ||
                        componentsToDelete.contains(connection->TargetComponentNumber))
                    {
                        connectionsToDelete.append(connection);
                    }
                }

                // 删除OR块上对应的输入引脚
                int orPinId = outputConnectionList.first()->TargetPinId;

                // 先收集并删除所有涉及被删除元件的连接
                QList<QSharedPointer<LDConnection>> connectionsToDelete;
                for (auto &connection : ld->connections->connectionList)
                {
                    for (int compNum : componentsToDelete)
                    {
                        if (connection->SourceComponentNumber == compNum ||
                            connection->TargetComponentNumber == compNum)
                        {
                            connectionsToDelete.append(connection);
                            break;
                        }
                    }
                }

                // 删除所有相关连接
                for (auto &connection : connectionsToDelete)
                {
                    ld->connections->connectionList.removeOne(connection);
                    LOG_INFO_DEFAULT("删除连接: 源元件={}, 源引脚={}, 目标元件={}, 目标引脚={}",
                                     connection->SourceComponentNumber, connection->SourcePinId,
                                     connection->TargetComponentNumber, connection->TargetPinId);
                }

                // 找到并删除对应的引脚
                for (int i = 0; i < outputTargetComponent->connectorList.size(); i++)
                {
                    if (outputTargetComponent->connectorList[i]->PinId == orPinId)
                    {
                        outputTargetComponent->connectorList.removeAt(i);
                        outputTargetComponent->InputPinNum--;
                        LOG_INFO_DEFAULT("删除OR块的引脚: PinId={}", orPinId);

                        // 更新后续引脚的ID
                        for (auto &connector : outputTargetComponent->connectorList)
                        {
                            if (connector->Direction == "Input" && connector->PinId > orPinId)
                            {
                                connector->PinId--;
                                connector->Name = "IN" + QString::number(connector->PinId - 1);
                            }
                        }

                        // 更新相关连接的引脚ID
                        for (auto &conn : ld->connections->connectionList)
                        {
                            if (conn->TargetComponentNumber == outputTargetComponent->Number &&
                                conn->TargetPinId > orPinId)
                            {
                                conn->TargetPinId--;
                            }
                        }
                        break;
                    }
                }
                // 重新计算引脚位置
                outputTargetComponent->setPinPosOffset();

                // 如果有输入源，删除对应的输出引脚
                if (sourceComponent && inputConnection)
                {
                    deleteExtraOutConnectors(fileKey, sourceComponent->Number);
                }
            }
        }
    }
    else
    {
        // 判断是否有多个输出连接
        if (outputConnectionList.size() > 1)
        {
            LOG_INFO_DEFAULT("存在多个输出连接，数量: {}", outputConnectionList.size());
            // 输出详细的连接信息用于调试
            for (int i = 0; i < outputConnectionList.size(); ++i)
            {
                auto &connection = outputConnectionList[i];
                auto &targetComponent = outputTargetComponentList[i];
                LOG_INFO_DEFAULT("输出连接{}: 源元件{}的引脚{} -> 目标元件{}的引脚{}",
                                 connection->SourceComponentNumber, connection->SourcePinId, targetComponent->Number,
                                 connection->TargetPinId);
            }
            // 收集所有需要删除的连接
            for (auto &connection : ld->connections->connectionList)
            {
                for (int compNum : componentsToDelete)
                {
                    if (connection->SourceComponentNumber == compNum || connection->TargetComponentNumber == compNum)
                    {
                        connectionsToDelete.append(connection);
                        break;
                    }
                }
            }

            // 删除所有连接
            for (auto &connection : connectionsToDelete)
            {
                ld->connections->connectionList.removeOne(connection);
            }

            // 如果有输入和多个输出连接，建立正确的一对一映射连接
            if (sourceComponent)
            {
                // 确保输出连接列表和目标元件列表数量一致
                int connectionCount = qMin(outputConnectionList.size(), outputTargetComponentList.size());
                LOG_INFO_DEFAULT("重建{}个输出连接", connectionCount);

                for (int i = 0; i < connectionCount; ++i)
                {
                    auto &outputConnection = outputConnectionList[i];
                    auto &outputTargetComponent = outputTargetComponentList[i];

                    // 使用被删除元件的输出引脚ID作为源引脚ID，保持原有的引脚对应关系
                    int sourcePinId = outputConnection->SourcePinId;

                    // 检查源元件是否已有对应的输出引脚，如果没有则添加
                    bool hasOutputPin = false;
                    for (auto &connector : sourceComponent->connectorList)
                    {
                        if (connector->Direction == "Output" && connector->PinId == sourcePinId)
                        {
                            hasOutputPin = true;
                            break;
                        }
                    }

                    if (!hasOutputPin)
                    {
                        // 添加输出引脚
                        int newPinId = addOUTConnector(fileKey, sourceComponent->Number);
                        LOG_INFO_DEFAULT("为源元件{}添加输出引脚，期望PinId={}，实际PinId={}", sourceComponent->Number,
                                         sourcePinId, newPinId);
                        // 注意：addOUTConnector可能返回的PinId与期望的不同，这里使用实际返回的PinId
                        sourcePinId = newPinId;
                    }

                    // 检查连接是否已存在，避免重复建立
                    bool connectionExists = false;
                    for (auto &existingConnection : ld->connections->connectionList)
                    {
                        if (existingConnection->SourceComponentNumber == sourceComponent->Number &&
                            existingConnection->SourcePinId == sourcePinId &&
                            existingConnection->TargetComponentNumber == outputTargetComponent->Number &&
                            existingConnection->TargetPinId == outputConnection->TargetPinId)
                        {
                            connectionExists = true;
                            LOG_INFO_DEFAULT("连接已存在，跳过: {}的引脚{} -> {}的引脚{}", sourceComponent->Number,
                                             sourcePinId, outputTargetComponent->Number, outputConnection->TargetPinId);
                            break;
                        }
                    }

                    if (!connectionExists)
                    {
                        QSharedPointer<LDConnection> newConnection =
                            addConnection(fileKey, sourceComponent->Number, sourcePinId,
                                          outputConnection->SourceConnectIndex, outputTargetComponent->Number,
                                          outputConnection->TargetPinId, outputConnection->TargetConnectIndex, true);
                        if (nullptr == newConnection)
                        {
                            LOG_ERROR_DEFAULT("重新建立连接失败:{}的引脚{} -> {}的引脚{}", sourceComponent->Number,
                                              sourcePinId, outputTargetComponent->Number,
                                              outputConnection->TargetPinId);
                        }
                        else
                        {
                            LOG_INFO_DEFAULT("重新建立连接: {}的引脚{} -> {}的引脚{}", sourceComponent->Number,
                                             sourcePinId, outputTargetComponent->Number, outputConnection->TargetPinId);
                        }
                    }
                }
            }
            else
            {
                LOG_INFO_DEFAULT("没有输入连接");
            }
        }
        else
        {
            LOG_INFO_DEFAULT("{}元件只有一个输出连接", compNumber);
            // 只有一个输出连接
            QSharedPointer<LDConnection> outputConnection = outputConnectionList.first();
            QSharedPointer<LDComponent> outputTargetComponent = outputTargetComponentList.first();

            LOG_INFO_DEFAULT("只有一个输出连接: {}元件的源引脚{} -> 目标元件{}的引脚{}",
                             outputConnection->SourceComponentNumber, outputConnection->SourcePinId,
                             outputTargetComponent->Number, outputConnection->TargetPinId);

            // 收集所有需要删除的连接
            for (auto &connection : ld->connections->connectionList)
            {
                for (int compId : componentsToDelete)
                {
                    if (connection->SourceComponentNumber == compId || connection->TargetComponentNumber == compId)
                    {
                        connectionsToDelete.append(connection);
                        break;
                    }
                }
            }

            // 删除所有连接
            for (auto &connection : connectionsToDelete)
            {
                ld->connections->connectionList.removeOne(connection);
            }

            // 如果有输入和输出连接，建立直接连接
            if (sourceComponent && outputTargetComponent && inputConnection && outputConnection)
            {
                // 使用被删除元件的输出引脚ID作为源引脚ID，保持原有的引脚对应关系
                QSharedPointer<LDConnection> newConnetion =
                    addConnection(fileKey, inputConnection->SourceComponentNumber, inputConnection->SourcePinId,
                                  inputConnection->SourceConnectIndex, outputTargetComponent->Number,
                                  outputConnection->TargetPinId, outputConnection->TargetConnectIndex, true);
                if (nullptr == newConnetion)
                {
                    LOG_ERROR_DEFAULT("重新建立连接失败:{}的引脚{} -> {}的引脚{}",
                                      inputConnection->SourceComponentNumber, inputConnection->SourcePinId,
                                      outputTargetComponent->Number, outputConnection->TargetPinId);
                }
                LOG_INFO_DEFAULT("重新建立连接: {}的引脚{} -> {}的引脚{}", inputConnection->SourceComponentNumber,
                                 inputConnection->SourcePinId, outputTargetComponent->Number,
                                 outputConnection->TargetPinId);
            }
        }
    }

    // 4. 删除所有相关元件
    for (int compId : componentsToDelete)
    {
        if (ld->components->componentMap.contains(compId))
        {
            QSharedPointer<LDComponent> comp = ld->searchComponentFromNumber(compId);

            // 从网络中移除
            if (ld->networks->networkMap.contains(comp->NetworkNumber))
            {
                QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(comp->NetworkNumber);
                network->componentMap.remove(compId);
            }

            // 从全局映射中移除
            ld->components->componentMap.remove(compId);

            LOG_INFO_DEFAULT("删除元件: {}", compId);
        }
    }

    // 5. 收集所有受影响的元件并重新排序连接索引
    QList<int> affectedComponents;

    // 添加源元件（如果存在）
    if (sourceComponent)
    {
        affectedComponents.append(sourceComponent->Number);
    }

    // 添加所有输出目标元件
    for (auto &targetComponent : outputTargetComponentList)
    {
        if (targetComponent && !affectedComponents.contains(targetComponent->Number))
        {
            affectedComponents.append(targetComponent->Number);
        }
    }

    // 重新排序连接索引，确保删除触点后索引的连续性
    if (!affectedComponents.isEmpty())
    {
        LOG_INFO_DEFAULT("开始重新排序受影响元件的连接索引，元件数量: {}", affectedComponents.size());
        reorderConnectionIndexes(fileKey, affectedComponents);
    }

    LOG_INFO_DEFAULT("删除触点完成");
    return true;
}

// 添加块元件
int LDManager::addBlockComponent(const QString &fileKey, int networkNumber, int compNumber, QJsonObject param,
                                 int leftOrRight)
{
    // 打印param
    QJsonDocument doc(param);
    QString jsonString = doc.toJson(QJsonDocument::Indented);
    LOG_INFO_DEFAULT("参数内容:\n{}", jsonString.toStdString());
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        if (ld->networks->networkMap.contains(networkNumber))
        {
            // 找到对应网络
            QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);

            // 检测是否为扩展块元件
            bool isExtendBlock = false;
            QString childType = param["ChildType"].toString();
            if (childType.contains("[Advance]") || childType == "Func")
            {
                QString auxContent = param["AuxContent"].toString();
                if (auxContent.contains("TYPECAST_FUNC") || auxContent.contains("REDUCE_FUNC"))
                {
                    isExtendBlock = true;
                }
            }
            QSharedPointer<LDComponent> newComp = nullptr;
            if (isExtendBlock)
            {
                newComp = createExtendComponent(fileKey, networkNumber, param);
            }
            else
            {
                newComp = createBlockComponent(fileKey, networkNumber, param);
            }

            // 在目标元件左侧串联块元件
            if (0 == leftOrRight)
            {
                if (!addBlockComponentToLeft(fileKey, networkNumber, compNumber, newComp))
                {
                    LOG_ERROR_DEFAULT("在目标元件左侧串联块元件失败");
                    return -1;
                }
            }
            // 在目标元件右侧串联块元件
            else if (2 == leftOrRight)
            {
                if (!addBlockComponentToRight(fileKey, networkNumber, compNumber, newComp))
                {
                    LOG_ERROR_DEFAULT("在目标元件右侧串联块元件失败");
                    return -1;
                }
            }
            return newComp->Number;
        }
    }
    return -1;
}

int LDManager::createNewVariableComponent(const QString &fileKey, int networkNumber, int leftOrRight, int parentNumber,
                                          int parentPinId, const QString &pinDataType, int xpos, int ypos)
{
    int reNubmer = 0;
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        if (ld->networks->networkMap.contains(networkNumber))
        {
            // 找到对应网络
            QSharedPointer<LDNetwork> nw = ld->networks->networkMap.value(networkNumber);
            int newNumber = ld->getMaxComonpentNumber() + 1;

            QString strUUID = getUUID();

            QSharedPointer<LDComponent> newComp = QSharedPointer<LDComponent>(new LDComponent());
            newComp->Name = "Variable_" + strUUID.left(5);
            newComp->InstanceName = pinDataType + "_" + strUUID;
            newComp->NetworkNumber = nw->Number;
            newComp->Enable = true;
            newComp->Number = newNumber;
            newComp->TaskName = "";
            newComp->TaskOrderNumber = 0;
            newComp->Type = "Variable";
            newComp->ChildType = "Local"; // 作用域
            newComp->AuxContent = "???";  // 变量名
            newComp->AuxContent1 = "???"; // 所属文件
            newComp->DataType_Local = "";

            newComp->SupportsInPinType = "";
            newComp->SupportsOutPinType = "";
            newComp->SupportsInPinAdd = false;
            newComp->SupportsOutPinAdd = false;
            newComp->XPos = leftOrRight == 0 ? xpos - 6 : xpos;
            newComp->YPos = ypos;
            newComp->Height = 1;
            newComp->Width = 6;
            newComp->Visible = true;
            newComp->LeftOrRight = leftOrRight;
            newComp->ParentNumber = parentNumber;
            newComp->ParentPinId = parentPinId;
            newComp->ParentPinDataType = pinDataType;

            // 根据LeftOrRight确定引脚是输入还是输出
            newComp->InputPinNum = leftOrRight == 0 ? 0 : 1;
            newComp->OutputPinNum = leftOrRight == 2 ? 0 : 1;
            // 引脚 IN 或 OUT
            strUUID = getUUID();
            QSharedPointer<LDConnector> pinC = QSharedPointer<LDConnector>(new LDConnector());
            pinC->InstanceName = (leftOrRight == 0 ? "OUT_" : "IN_") + strUUID;
            pinC->PinId = leftOrRight == 0 ? -1 : 1;
            pinC->NewAdd = false;
            pinC->Name = leftOrRight == 0 ? "OUT" : "IN";
            pinC->DataType = pinDataType;
            pinC->SupportChangeDataType = false;
            pinC->Negated = false;
            pinC->Direction = leftOrRight == 0 ? "Output" : "Input";
            pinC->FastSignal = false;
            pinC->isInitVar = false;
            pinC->InitValue = "";
            pinC->XPos = leftOrRight == 0 ? newComp->Width : 0;
            pinC->YPos = 0;
            pinC->Visible = false; // 变量元件引脚不显示
            pinC->IsLogical = false;
            pinC->ChildNumber = -1;

            newComp->connectorList.append(pinC);

            // 加入
            ld->components->componentMap.insert(newComp->Number, newComp);

            // 添加对应关系
            newComp->network = nw;
            nw->componentMap.insert(newComp->Number, newComp);
            reNubmer = newComp->Number;
        }
    }
    return reNubmer;
}

QSharedPointer<LDComponent> LDManager::createBlockComponent(const QString &fileKey, int networkNumber,
                                                            QJsonObject param)
{
    QSharedPointer<LDComponent> newComp = nullptr;
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        if (ld->networks->networkMap.contains(networkNumber))
        {
            // 找到对应网络
            QSharedPointer<LDNetwork> nw = ld->networks->networkMap.value(networkNumber);
            int newNumber = ld->getMaxComonpentNumber() + 1;

            QString strUUID = QUuid::createUuid().toString().remove("{").remove("}").remove("-");

            newComp = QSharedPointer<LDComponent>(new LDComponent());
            newComp->Name = param["InstanceName"].toString() + strUUID.left(5);
            newComp->InstanceName = param["AuxContent"].toString() + "_" + strUUID;
            newComp->NetworkNumber = nw->Number;
            newComp->Enable = true;
            newComp->Number = newNumber;
            newComp->TaskName = ld->TasksName == "" ? "T2" : ld->TasksName; // 块任务默认cfc模式下生效，其它同文件任务
            newComp->TaskOrderNumber = ld->getMaxOrderNumbber(newComp->TaskName) + 1;
            newComp->Type = "Block";
            newComp->ChildType = param["ChildType"].toString();
            newComp->AuxContent = param["AuxContent"].toString();
            newComp->DataType_Local = "";

            newComp->SupportsInPinType = param["SupportsInPinType"].toString();
            newComp->SupportsOutPinType = param["SupportsOutPinType"].toString();
            newComp->SupportsInPinAdd = false;
            newComp->SupportsOutPinAdd = false;
            newComp->XPos = param["XPos"].toInt();
            newComp->YPos = param["YPos"].toInt();
            newComp->Height = param["Height"].toInt();
            newComp->Width = 11;
            newComp->Visible = true;
            newComp->LeftOrRight = 1;
            newComp->ParentNumber = -1;
            newComp->ParentPinId = 0;

            // 根据子类型确定引脚
            // FUNC 函数
            // FB 功能块
            int inpinId = 1;   // 从EN开始
            int outpinId = -1; // 从ENO开始
            QJsonArray connectors = param["connectors"].toArray();
            // 先增加EN和ENO两个引脚
            // EN引脚
            strUUID = getUUID();
            QSharedPointer<LDConnector> enpin = QSharedPointer<LDConnector>(new LDConnector());
            enpin->InstanceName = "EN_" + strUUID;
            enpin->PinId = inpinId;
            inpinId++;
            enpin->NewAdd = false;
            enpin->Name = "EN";
            enpin->DataType = "BOOL";
            enpin->SupportChangeDataType = false;
            enpin->Negated = false;
            enpin->Direction = "Input";
            enpin->FastSignal = false;
            enpin->isInitVar = false;
            enpin->InitValue = "";
            enpin->IsLogical = true;
            enpin->Visible = true;
            enpin->XPos = 0;
            enpin->YPos = 0;

            newComp->connectorList.append(enpin);
            // ENO引脚
            strUUID = getUUID();
            QSharedPointer<LDConnector> enopin = QSharedPointer<LDConnector>(new LDConnector());
            enopin->InstanceName = "ENO_" + strUUID;
            enopin->PinId = outpinId;
            outpinId--;
            enopin->NewAdd = false;
            enopin->Name = "ENO";
            enopin->DataType = "BOOL";
            enopin->SupportChangeDataType = false;
            enopin->Negated = false;
            enopin->Direction = "Output";
            enopin->FastSignal = false;
            enopin->isInitVar = false;
            enopin->InitValue = "";
            enopin->IsLogical = true;
            enopin->Visible = true;
            enopin->XPos = 0;
            enopin->YPos = 0;

            newComp->connectorList.append(enopin);

            // 根据传入的引脚信息进行引脚与挂载变量和链接的添加
            for (int i = 0; i < connectors.count(); ++i)
            {
                if (connectors[i].isObject())
                {
                    QJsonObject obj = connectors[i].toObject();

                    strUUID = getUUID();
                    QSharedPointer<LDConnector> pin = QSharedPointer<LDConnector>(new LDConnector());
                    pin->InstanceName = obj["Name"].toString() + "_" + strUUID;
                    if (obj["Direction"].toString() == "Input")
                    {
                        pin->PinId = inpinId;
                        inpinId++;
                    }
                    else if (obj["Direction"].toString() == "Output")
                    {
                        pin->PinId = outpinId;
                        outpinId--;
                    }
                    pin->NewAdd = false;
                    pin->Name = obj["Name"].toString();
                    pin->DataType = obj["DataType"].toString();
                    pin->SupportChangeDataType = false;
                    pin->Negated = false;
                    pin->Direction = obj["Direction"].toString();
                    pin->FastSignal = false;
                    pin->isInitVar = false;
                    pin->InitValue = "";
                    pin->IsLogical = false;
                    pin->Visible = true;
                    pin->XPos = 0;
                    pin->YPos = 0;

                    newComp->connectorList.append(pin);
                }
            }
            // 进行引脚定位处理XPos YPos
            newComp->setPinPosOffset();
            // 加入
            ld->components->componentMap.insert(newComp->Number, newComp);

            // 添加对应关系
            newComp->network = nw;
            nw->componentMap.insert(newComp->Number, newComp);

            LOG_INFO_DEFAULT("connectorList:{}", newComp->connectorList.count());
            // 对应引脚 添加 绑定的变量元件 和 链接
            for (QSharedPointer<LDConnector> &pin : newComp->connectorList)
            {
                if (!pin->IsLogical)
                {
                    // 非逻辑控制引脚
                    int leftOrRight = pin->Direction == "Input" ? 0 : 2;
                    int xpos = newComp->XPos + pin->XPos;
                    int ypos = newComp->YPos + pin->YPos;
                    int newVarCompNumber = createNewVariableComponent(
                        fileKey, networkNumber, leftOrRight, newComp->Number, pin->PinId, pin->DataType, xpos, ypos);
                    if (newVarCompNumber > 0)
                    {
                        int sourceConnectIndex = getMaxSourceConnectIndex(fileKey, newComp->Number) + 1;
                        int targetConnectIndex = getMaxTargetConnectIndex(fileKey, newVarCompNumber) + 1;
                        // 建立链接
                        QSharedPointer<LDConnection> newConnection =
                            addConnection(fileKey, newComp->Number, pin->PinId, sourceConnectIndex, newVarCompNumber,
                                          (pin->Direction == "Input" ? -1 : 1), targetConnectIndex, false);
                        if (nullptr == newConnection)
                        {
                            pin->ChildNumber = newVarCompNumber;
                        }
                    }
                }
            }
        }
    }
    return newComp;
}

QSharedPointer<LDComponent> LDManager::createExtendComponent(const QString &fileKey, int networkNumber,
                                                             QJsonObject param)
{
    QSharedPointer<LDComponent> newComp = nullptr;
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        if (ld->networks->networkMap.contains(networkNumber))
        {
            // 找到对应网络
            QSharedPointer<LDNetwork> nw = ld->networks->networkMap.value(networkNumber);
            int newNumber = ld->getMaxComonpentNumber() + 1;

            QString strUUID = getUUID();

            newComp = QSharedPointer<LDComponent>(new LDComponent());
            newComp->Name = param["InstanceName"].toString() + "_" + strUUID.left(5);
            newComp->AuxContent = param["ChildType"].toString().replace("[Advance]", "");
            newComp->InstanceName = newComp->AuxContent + "_" + strUUID;
            newComp->ChildType = "FUNC";
            newComp->NetworkNumber = nw->Number;
            newComp->Enable = true;
            newComp->Number = newNumber;
            newComp->TaskName = ld->TasksName == "" ? "T2" : ld->TasksName; // 块任务默认cfc模式下生效，其它同文件任务
            newComp->TaskOrderNumber = ld->getMaxOrderNumbber(newComp->TaskName) + 1;
            newComp->Type = "Block";

            newComp->XPos = param["XPos"].toInt();
            newComp->YPos = param["YPos"].toInt();
            newComp->Height = param["Height"].toInt();
            newComp->Width = 11;
            newComp->Visible = true;
            newComp->LeftOrRight = 1;
            newComp->ParentNumber = -1;
            newComp->ParentPinId = 0;

            QJsonArray connectors;

            // 取扩展文件
            QString exfbcPath = appDir + "/Settings/extend_fbc.xml";
            ExtendFBC fbc;
            fbc.fromXml(exfbcPath);

            for (int i = 0; i < fbc.componentList->componentList.size(); i++)
            {
                QSharedPointer<ExComponent> com = fbc.componentList->componentList[i];
                if (com->ChildType == newComp->AuxContent)
                {
                    if (newComp->AuxContent == "TYPECAST_FUNC")
                    {
                        newComp->SupportsInPinAdd = false;
                        newComp->SupportsOutPinAdd = false;
                        // 提取输入类型
                        QStringList inList;
                        QStringList outList;
                        QStringList dt = com->dataType.replace("[{'", "").replace("'}]", "").split("','");
                        for (const QString &da : dt)
                        {
                            QStringList datype = da.split("2");
                            if (datype.size() > 0)
                            {
                                inList << datype[0];
                                outList << datype[1];
                            }
                        }
                        newComp->SupportsInPinType = inList.join(",");
                        newComp->SupportsOutPinType = outList.join(",");

                        // qDebug() << "inList:" << inList;
                        // qDebug() << "outList:" << outList;
                        // 引脚信息
                        QJsonObject inobj;
                        inobj["Name"] = "IN";
                        inobj["Direction"] = "Input";
                        inobj["DataType"] = inList[0];
                        inobj["SupportChangeDataType"] = true;
                        inobj["DataTypeGroup"] = "local_0";
                        connectors.append(inobj);

                        QJsonObject outobj;
                        outobj["Name"] = "OUT";
                        outobj["Direction"] = "Output";
                        outobj["SupportChangeDataType"] = true;
                        outobj["DataType"] = outList[0].replace("[", "").replace("]", "").split(",")[0];
                        outobj["DataTypeGroup"] = "local_1";
                        connectors.append(outobj);
                    }
                    else if (newComp->AuxContent.indexOf("REDUCE_FUNC") >= 0)
                    {
                        // 输入引脚配置
                        newComp->SupportsInPinAdd = false;
                        int pinIndex = 1;
                        for (int k = 0; k < com->var->input->elementList.size(); k++)
                        {
                            QSharedPointer<ExElement> el = com->var->input->elementList[k];
                            QString dataType = el->dataType;
                            // 引脚可变类型
                            if (el->dataType.indexOf("local") > -1)
                            {
                                newComp->SupportsInPinType =
                                    com->dataType.replace("[{'", "").replace("'}]", "").replace("','", ",");
                                dataType = newComp->SupportsInPinType.split(",")[0];
                            }
                            // 可增加引脚
                            if (el->ListMode)
                            {
                                newComp->SupportsInPinAdd = true;
                                QJsonObject moreobj;
                                moreobj["Name"] = "IN" + QString::number(pinIndex);
                                moreobj["Direction"] = "Input";
                                moreobj["DataType"] = dataType;
                                moreobj["SupportChangeDataType"] = el->dataType.indexOf("local") > -1;
                                moreobj["DataTypeGroup"] = el->dataType;
                                connectors.append(moreobj);
                                pinIndex++;

                                QJsonObject more2obj;
                                more2obj["Name"] = "IN" + QString::number(pinIndex);
                                more2obj["Direction"] = "Input";
                                more2obj["DataType"] = dataType;
                                more2obj["SupportChangeDataType"] = el->dataType.indexOf("local") > -1;
                                more2obj["DataTypeGroup"] = el->dataType;
                                connectors.append(more2obj);
                                pinIndex++;
                            }
                            else
                            {
                                QJsonObject inobj;
                                inobj["Name"] = el->Name;
                                inobj["Direction"] = "Input";
                                inobj["DataType"] = dataType;
                                inobj["SupportChangeDataType"] = el->dataType.indexOf("local") > -1;
                                inobj["DataTypeGroup"] = el->dataType;
                                connectors.append(inobj);

                                pinIndex++;
                            }
                        }
                        // 输出引脚配置
                        newComp->SupportsOutPinType = "follow";
                        newComp->SupportsOutPinAdd = false;
                        pinIndex = 1;
                        for (int k = 0; k < com->var->output->elementList.size(); k++)
                        {
                            QSharedPointer<ExElement> el = com->var->output->elementList[k];
                            QString dataType = el->dataType;
                            // 引脚可变类型
                            if (el->dataType.indexOf("local") > -1)
                            {
                                newComp->SupportsOutPinType =
                                    com->dataType.replace("[{'", "").replace("'}]", "").replace("','", ",");
                                dataType = newComp->SupportsOutPinType.split(",")[0];
                            }
                            // 可增加引脚
                            if (el->ListMode)
                            {
                                newComp->SupportsOutPinAdd = true;
                                QJsonObject outobj;
                                outobj["Name"] = "OUT" + QString::number(pinIndex);
                                outobj["Direction"] = "Output";
                                outobj["DataType"] = dataType;
                                outobj["SupportChangeDataType"] = el->dataType.indexOf("local") > -1;
                                outobj["DataTypeGroup"] = el->dataType;
                                connectors.append(outobj);

                                pinIndex++;
                            }
                            else
                            {
                                QJsonObject outobj;
                                outobj["Name"] = el->Name;
                                outobj["Direction"] = "Output";
                                outobj["DataType"] = dataType;
                                outobj["SupportChangeDataType"] = el->dataType.indexOf("local") > -1;
                                outobj["DataTypeGroup"] = el->dataType;
                                connectors.append(outobj);
                            }
                        }
                    }
                    break;
                }
            }

            int inpinId = 1;
            int outpinId = -1;
            // 先增加EN和ENO两个引脚
            // EN引脚
            strUUID = getUUID();
            QSharedPointer<LDConnector> enpin = QSharedPointer<LDConnector>(new LDConnector());
            enpin->InstanceName = "EN_" + strUUID;
            enpin->PinId = inpinId;
            inpinId++;
            enpin->NewAdd = false;
            enpin->Name = "EN";
            enpin->DataType = "BOOL";
            enpin->SupportChangeDataType = false;
            enpin->Negated = false;
            enpin->Direction = "Input";
            enpin->FastSignal = false;
            enpin->isInitVar = false;
            enpin->InitValue = "";
            enpin->IsLogical = true;
            enpin->Visible = true;
            enpin->XPos = 0;
            enpin->YPos = 0;

            newComp->connectorList.append(enpin);
            // ENO引脚
            strUUID = getUUID();
            QSharedPointer<LDConnector> enopin = QSharedPointer<LDConnector>(new LDConnector());
            enopin->InstanceName = "ENO_" + strUUID;
            enopin->PinId = outpinId;
            outpinId--;
            enopin->NewAdd = false;
            enopin->Name = "ENO";
            enopin->DataType = "BOOL";
            enopin->SupportChangeDataType = false;
            enopin->Negated = false;
            enopin->Direction = "Output";
            enopin->FastSignal = false;
            enopin->isInitVar = false;
            enopin->InitValue = "";
            enopin->IsLogical = true;
            enopin->Visible = true;
            enopin->XPos = 0;
            enopin->YPos = 0;

            newComp->connectorList.append(enopin);

            // qDebug() << "connectors.count()" << connectors.count();
            for (int i = 0; i < connectors.count(); ++i)
            {
                if (connectors[i].isObject())
                {
                    QJsonObject obj = connectors[i].toObject();

                    strUUID = getUUID();
                    QSharedPointer<LDConnector> pin = QSharedPointer<LDConnector>(new LDConnector());
                    pin->InstanceName = obj["Name"].toString() + "_" + strUUID;
                    if (obj["Direction"].toString() == "Input")
                    {
                        pin->PinId = inpinId;
                        inpinId++;
                    }
                    else if (obj["Direction"].toString() == "Output")
                    {
                        pin->PinId = outpinId;
                        outpinId--;
                    }
                    pin->NewAdd = false;
                    pin->Name = obj["Name"].toString();
                    pin->DataType = obj["DataType"].toString();
                    pin->SupportChangeDataType = obj["SupportChangeDataType"].toBool();
                    pin->DataTypeGroup = obj["DataTypeGroup"].toString();
                    pin->Negated = false;
                    pin->Direction = obj["Direction"].toString();
                    pin->FastSignal = false;
                    pin->isInitVar = false;
                    pin->InitValue = "";
                    pin->XPos = 0;
                    pin->YPos = 0;
                    pin->IsLogical = false;
                    pin->Visible = true;

                    newComp->connectorList.append(pin);
                }
            }
            // 进行引脚定位处理XPos YPos
            newComp->setPinPosOffset();
            // 更新所选引脚的数据类型到块上
            newComp->updateDataTypeLocal();
            // 加入
            ld->components->componentMap.insert(newComp->Number, newComp);

            // 添加对应关系
            newComp->network = nw;
            nw->componentMap.insert(newComp->Number, newComp);

            LOG_INFO_DEFAULT("connectorList:{}", newComp->connectorList.count());

            // 对应引脚 添加 绑定的变量元件 和 链接
            for (QSharedPointer<LDConnector> &pin : newComp->connectorList)
            {
                if (!pin->IsLogical)
                {
                    // 非逻辑控制引脚
                    int leftOrRight = pin->Direction == "Input" ? 0 : 2;
                    int xpos = newComp->XPos + pin->XPos;
                    int ypos = newComp->YPos + pin->YPos;
                    int newVarCompNumber = createNewVariableComponent(
                        fileKey, networkNumber, leftOrRight, newComp->Number, pin->PinId, pin->DataType, xpos, ypos);
                    if (newVarCompNumber > 0)
                    {
                        int sourceConnectIndex = getMaxSourceConnectIndex(fileKey, newComp->Number) + 1;
                        int targetConnectIndex = getMaxTargetConnectIndex(fileKey, newVarCompNumber) + 1;
                        // 建立链接
                        QSharedPointer<LDConnection> newConnection =
                            addConnection(fileKey, newComp->Number, pin->PinId, sourceConnectIndex, newVarCompNumber,
                                          (pin->Direction == "Input" ? -1 : 1), targetConnectIndex, false);
                        if (nullptr != newConnection)
                        {
                            pin->ChildNumber = newVarCompNumber;
                        }
                    }
                }
            }
        }
        if (ld->Code == "LD")
        {
            ld->sortAllTaskOrderNumber();
        }
    }
    return newComp;
}

// 在指定元件右侧添加块元件
bool LDManager::addBlockComponentToRight(const QString &fileKey, int networkNumber, int compNumber,
                                         QSharedPointer<LDComponent> newComp)
{
    LOG_INFO_DEFAULT("将块元件水平串联到元件{}右边", compNumber);
    // 检查文件是否存在
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在");
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    if (!newComp)
    {
        LOG_ERROR_DEFAULT("元件不存在");
        return false;
    }

    // 检查插入位置元件是否存在
    QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
    if (!com_ptr)
    {
        LOG_ERROR_DEFAULT("目标元件不存在: compNumber={}", compNumber);
        return false;
    }

    // 收集插入位置元件输出的所有链接
    QList<QSharedPointer<LDConnection>> outConnections;

    // 遍历所有链接
    for (auto &connection : ld->connections->connectionList)
    {
        // 连接源为插入位置元件
        if (connection->SourceComponentNumber == com_ptr->Number)
        {
            // 判断connection->TargetComponentNumber是否为变量元件
            if (ld->components->componentMap.contains(connection->TargetComponentNumber))
            {
                QSharedPointer<LDComponent> targetComponent =
                    ld->components->componentMap[connection->TargetComponentNumber];
                if (targetComponent->Type != "Variable" && targetComponent->ChildType != "Local")
                {
                    outConnections.append(connection);
                }
            }
        }
        // 按照connection->SourceConnectIndex从大到小排序
        std::sort(outConnections.begin(), outConnections.end(),
                  [](const QSharedPointer<LDConnection> &a, const QSharedPointer<LDConnection> &b) {
                      return a->SourceConnectIndex > b->SourceConnectIndex;
                  });
    }

    // 为新元件的在EN和ENO之下添加输入和输出引脚用于connection
    QSharedPointer<LDConnector> newOutConnector = insertOUTConnector(fileKey, newComp->Number, -1);
    if (!newOutConnector)
    {
        LOG_ERROR_DEFAULT("新元件插入OUT引脚失败");
        return false;
    }

    for (auto &connection : outConnections)
    {
        // 修改连接源为插入元件
        connection->SourceComponentNumber = newComp->Number;
        QSharedPointer<LDConnector> newOutConnector = getFirstOUTConnector(fileKey, newComp->Number);
        if (!newOutConnector)
        {
            LOG_ERROR_DEFAULT("获取新元件OUT引脚失败");
            return false;
        }
    }

    // 元件添加IN引脚
    QSharedPointer<LDConnector> newInConnector = insertINConnector(fileKey, newComp->Number, 1);
    LOG_INFO_DEFAULT("{}元件插入IN引脚{}", newComp->Number, newInConnector->PinId);
    if (newInConnector->PinId != 2)
    {
        LOG_ERROR_DEFAULT("元件{}的IN引脚PinId{}错误", newComp->Number, newInConnector->PinId);
        return false;
    }
    // 获取插入位置的第一个OUT引脚
    QSharedPointer<LDConnector> outConnector = getFirstOUTConnector(fileKey, com_ptr->Number);
    if (!outConnector)
    {
        LOG_ERROR_DEFAULT("获取插入位置元件的OUT引脚失败");
        return false;
    }

    // 调整插入位置元件的OUT引脚连接索引
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        if (connection->SourceComponentNumber == com_ptr->Number)
        {
            if (connection->SourceConnectIndex >= 0)
            {
                connection->SourceConnectIndex++;
            }
        }
    }
    // 调整新元件的IN引脚连接索引
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        if (connection->TargetComponentNumber == newComp->Number)
        {
            if (connection->TargetConnectIndex >= 0)
            {
                connection->TargetConnectIndex++;
            }
        }
    }

    // 水平串联连接：插入位置元件OUT引脚 -> 新元件IN引脚
    QSharedPointer<LDConnection> newConnection = addConnection(fileKey, com_ptr->Number, outConnector->PinId, 0,
                                                               newComp->Number, newInConnector->PinId, 0, true);
    if (nullptr == newConnection)
    {
        LOG_ERROR_DEFAULT("插入位置元件{}的OUT引脚 -> 新元件{}的IN引脚连接失败", com_ptr->Number, newComp->Number);
        return false;
    }
    LOG_INFO_DEFAULT("{}元件的{}->{}元件的{}连接成功", com_ptr->Number, newConnection->SourceConnectIndex,
                     newComp->Number, newConnection->TargetConnectIndex);

    return true;
}

// 在指定元件左侧添加块元件
bool LDManager::addBlockComponentToLeft(const QString &fileKey, int networkNumber, int compNumber,
                                        QSharedPointer<LDComponent> newComp)
{
    LOG_INFO_DEFAULT("将块元件添加到元件{}左侧", compNumber);

    // 检查文件是否存在
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在");
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    if (!ld->networks->networkMap.contains(networkNumber))
    {
        LOG_ERROR_DEFAULT("网络不存在: networkNumber={}", networkNumber);
        return false;
    }

    // 检查目标元件是否存在
    QSharedPointer<LDComponent> targetComponent = ld->searchComponentFromNumber(compNumber);
    if (!targetComponent)
    {
        LOG_ERROR_DEFAULT("目标元件不存在: compNumber={}", compNumber);
        return false;
    }

    // 获取目标元件的输入引脚
    QSharedPointer<LDConnector> targetInConnector = getFirstINConnector(fileKey, compNumber);
    if (!targetInConnector)
    {
        LOG_ERROR_DEFAULT("获取目标元件IN引脚失败");
        return false;
    }
    LOG_INFO_DEFAULT("目标元件的IN引脚ID为{}", targetInConnector->PinId);

    // 获取目标元件输入引脚的现有连接
    QSharedPointer<LDConnection> existingConnection =
        getInputConnectorLDConnection(fileKey, compNumber, targetInConnector->PinId);
    if (!existingConnection)
    {
        LOG_ERROR_DEFAULT("获取目标元件IN引脚的连接失败");
        return false;
    }

    // 获取原来连接到目标元件的源元件信息
    int originalSourceComp = existingConnection->SourceComponentNumber;
    int originalSourcePin = existingConnection->SourcePinId;

    // 删除原有连接
    if (!deleteConnection(fileKey, originalSourceComp, originalSourcePin, compNumber, targetInConnector->PinId))
    {
        LOG_ERROR_DEFAULT("删除原有连接失败");
        return false;
    }

    // 为新元件的在EN和ENO之下添加输入和输出引脚用于connection
    QSharedPointer<LDConnector> newInConnector = insertINConnector(fileKey, newComp->Number, 1);
    if (!newInConnector)
    {
        LOG_ERROR_DEFAULT("新元件插入IN引脚失败");
        return false;
    }

    QSharedPointer<LDConnector> newOutConnector = insertOUTConnector(fileKey, newComp->Number, -1);
    if (!newOutConnector)
    {
        LOG_ERROR_DEFAULT("新元件插入OUT引脚失败");
        return false;
    }
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        if (connection->TargetComponentNumber == newComp->Number)
        {
            // 如果现有连接的索引 >= 插入索引，则需要递增
            if (connection->TargetConnectIndex >= 0)
            {
                LOG_INFO_DEFAULT("调整新元件块输入连接索引: 源元件={}, 旧索引={}, 新索引={}",
                                 connection->SourceComponentNumber, connection->TargetConnectIndex,
                                 connection->TargetConnectIndex + 1);
                connection->TargetConnectIndex++;
            }
        }
    }

    // 建立新的连接：原源元件 -> 新元件 -> 目标元件
    // 连接1：原源元件OUT引脚 -> 新元件IN引脚
    QSharedPointer<LDConnection> newConnection = addConnection(fileKey, originalSourceComp, originalSourcePin, 0,
                                                               newComp->Number, newInConnector->PinId, 0, true);
    if (nullptr == newConnection)
    {
        LOG_ERROR_DEFAULT("建立原源元件到新元件的连接失败");
        return false;
    }
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        if (connection->SourceComponentNumber == newComp->Number)
        {
            // 如果现有连接的索引 >= 插入索引，则需要递增
            if (connection->SourceConnectIndex >= 0)
            {
                LOG_INFO_DEFAULT("调整新元件块输出连接索引: 源元件={}, 旧索引={}, 新索引={}",
                                 connection->TargetComponentNumber, connection->SourceConnectIndex,
                                 connection->SourceConnectIndex + 1);
                connection->SourceConnectIndex++;
            }
        }
    }

    // 连接2：新元件OUT引脚 -> 目标元件IN引脚
    QSharedPointer<LDConnection> newConnection2 = addConnection(fileKey, newComp->Number, newOutConnector->PinId, 0,
                                                                compNumber, targetInConnector->PinId, 0, true);
    if (nullptr == newConnection2)
    {
        LOG_ERROR_DEFAULT("建立新元件到目标元件的连接失败");
        return false;
    }

    LOG_INFO_DEFAULT("块元件{}成功添加到元件{}左侧", newComp->Number, compNumber);
    return true;
}

// 删除块元件
bool LDManager::deleteBlockComponent(const QString &fileKey, int networkNumber, int compNumber)
{
    deleteContact(fileKey, compNumber);
    return true;
}

// 检查源引脚是否已有其他连接（连接到任何目标）结果如果存在，重用现有连接的实例名称和变量信息 return list[0]实例名称
// list[1]变量名称 list[2]变量所属文件
QStringList LDManager::haveConnectionFromSourcePin(const QString &fileKey, int sourceComponentNumber, int sourcePinId)
{
    QStringList list;
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        for (QSharedPointer<LDConnection> &connector : ld->connections->connectionList)
        {
            if (connector->SourceComponentNumber == sourceComponentNumber && connector->SourcePinId == sourcePinId)
            {
                list << connector->InstanceName << connector->VarName << connector->VarOwned;
            }
        }
    }
    return list;
}

// 向指定元件添加EN引脚和ENO引脚，并连接起始常量元件的ENO和该元件的EN
bool LDManager::addENAndENOConnector(const QString &fileKey, int compNumber)
{
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        // 检查元件是否存在
        if (ld->components->componentMap.contains(compNumber))
        {
            QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
            // 判断connectorList中是否存在EN和ENO引脚
            for (QSharedPointer<LDConnector> &connector : com_ptr->connectorList)
            {
                if (connector->Name == "EN" || connector->Name == "ENO")
                {
                    return false;
                }
            }

            // 添加EN和ENO引脚
            // 添加EN引脚
            QSharedPointer<LDConnector> enConnector = QSharedPointer<LDConnector>(new LDConnector());
            enConnector->InstanceName = "EN_" + QUuid::createUuid().toString().remove("{").remove("}").remove("-");
            enConnector->PinId = 1;
            enConnector->NewAdd = false;
            enConnector->Name = "EN";
            enConnector->DataType = "BOOL";

            enConnector->Direction = "EN";
            // 判断元件类型是否为Variable
            if (com_ptr->Type == "Variable")
            {
                enConnector->DataType = "Variable";
            }
            else
            {
                enConnector->DataType = "BOOL";
            }
            com_ptr->connectorList.append(enConnector);

            // 添加ENO引脚
            QSharedPointer<LDConnector> enoConnector = QSharedPointer<LDConnector>(new LDConnector());
            enoConnector->InstanceName = "ENO_" + QUuid::createUuid().toString().remove("{").remove("}").remove("-");
            enoConnector->PinId = -1;
            enoConnector->NewAdd = false;
            enoConnector->Name = "ENO";
            enoConnector->Direction = "ENO";
            // 判断元件类型是否为Variable
            if (com_ptr->Type == "Variable")
            {
                enoConnector->DataType = "Variable";
            }
            else
            {
                enoConnector->DataType = "BOOL";
            }
            com_ptr->connectorList.append(enoConnector);
        }
        else
        {
            LOG_ERROR_DEFAULT("LDComponent not found: {}", compNumber);
            return false;
        }
    }
    else
    {
        LOG_ERROR_DEFAULT("LDFile not found: {}", fileKey.toStdString().c_str());
        return false;
    }
    return true;
}

// 向指定元件添加IN引脚返回添加引脚的PinId
int LDManager::addINConnector(const QString &fileKey, int compNumber)
{
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        // 检查元件是否存在
        if (ld->components->componentMap.contains(compNumber))
        {
            QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
            if (nullptr == com_ptr)
            {
                LOG_ERROR_DEFAULT("元件不存在");
            }

            // 直接使用InputPinNum来确定新引脚的PinId
            // EN引脚的PinId是1，输入引脚从2开始递增
            int newPinId;
            // 判断该元件是否是变量元件
            if (com_ptr->Type == "Variable" && com_ptr->AuxContent1 != "Start")
            {
                // 变量元件没有EN引脚，所以输入引脚从1开始递增
                newPinId = com_ptr->OutputPinNum + 1;
            }
            else
            {
                // EN=1 输出引脚从2开始递增
                newPinId = com_ptr->OutputPinNum + 2;
            }

            // 添加IN引脚
            QSharedPointer<LDConnector> inConnector = QSharedPointer<LDConnector>(new LDConnector());
            inConnector->InstanceName = "IN_" + QUuid::createUuid().toString().remove("{").remove("}").remove("-");
            inConnector->PinId = newPinId;
            inConnector->NewAdd = false;
            inConnector->Name = "IN" + QString::number(newPinId - 1);
            inConnector->Direction = "Input";

            // 判断元件类型是否为Variable
            if (com_ptr->Type == "Variable")
            {
                inConnector->DataType = "Variable";
                inConnector->Visible = false; // 变量元件引脚不显示
            }
            else
            {
                inConnector->DataType = "BOOL";
            }

            // 添加引脚到元件
            com_ptr->connectorList.append(inConnector);

            // 更新元件的输入引脚数量
            com_ptr->InputPinNum++;

            // 重新计算引脚位置
            com_ptr->setPinPosOffset();

            return newPinId;
        }
        LOG_INFO_DEFAULT("元件{}不存在", compNumber);
        return 0;
    }
    return 0;
}

// 向指定元件添加OUT引脚
int LDManager::addOUTConnector(const QString &fileKey, int compNumber)
{
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        // 检查元件是否存在
        if (ld->components->componentMap.contains(compNumber))
        {
            // 删除未被使用的输出引脚
            deleteExtraOutConnectors(fileKey, compNumber);
            QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
            int newPinId;
            // 判断该元件是否是变量元件
            if (com_ptr->Type == "Variable" && com_ptr->AuxContent1 != "Start")
            {
                // 变量元件没有ENO引脚，所以输出引脚从-1开始递减
                newPinId = -(com_ptr->OutputPinNum + 1);
            }
            else
            {
                // ENO=-1 输出引脚从-2开始递减
                newPinId = -(com_ptr->OutputPinNum + 2);
            }

            // 添加OUT引脚
            QSharedPointer<LDConnector> outConnector = QSharedPointer<LDConnector>(new LDConnector());
            outConnector->InstanceName = "OUT_" + QUuid::createUuid().toString().remove("{").remove("}").remove("-");
            outConnector->PinId = newPinId;
            outConnector->NewAdd = false;
            outConnector->Name = "OUT" + QString::number(qAbs(newPinId) - 1);
            outConnector->Direction = "Output";

            // 判断元件类型是否为Variable
            if (com_ptr->Type == "Variable")
            {
                outConnector->DataType = "Variable";
                outConnector->Visible = false; // 变量元件引脚不显示
            }
            else
            {
                outConnector->DataType = "BOOL";
            }

            // 添加引脚到元件
            com_ptr->connectorList.append(outConnector);

            // 更新元件的输出引脚数量
            com_ptr->OutputPinNum++;

            // 重新计算引脚位置
            com_ptr->setPinPosOffset();

            return newPinId;
        }
        return 0;
    }
    return 0;
}

// 向指定元件的指定引脚位置下方插入IN引脚
QSharedPointer<LDConnector> LDManager::insertINConnector(const QString &fileKey, int compNumber, int pinId)
{
    LOG_INFO_DEFAULT("元件{}的引脚{}下方插入IN引脚", compNumber, pinId);
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        // 检查元件是否存在
        if (ld->components->componentMap.contains(compNumber))
        {
            QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);

            // 验证pinId是否是一个有效的输入引脚ID（正数）
            if (pinId <= 0)
            {
                return nullptr;
            }

            // 找到需要插入的位置（pinId之后的位置）
            // 计算新引脚的ID，应该是pinId+1（更正）
            int newPinId = pinId + 1;

            // 更新所有ID大于(更正)newPinId的引脚ID（包括newPinId）
            // 例如，如果newPinId是2，那么所有ID >= 2的引脚ID都需要+1
            for (QSharedPointer<LDConnector> &connector : com_ptr->connectorList)
            {
                if (connector->Direction == "Input" && connector->PinId >= newPinId)
                {
                    connector->PinId++; // 使ID更正
                    // 更新引脚名称以匹配新ID
                    if (connector->Name.startsWith("IN"))
                    {
                        connector->Name = "IN" + QString::number(connector->PinId);
                    }
                }
            }

            // 创建新的输入引脚
            QSharedPointer<LDConnector> inConnector = QSharedPointer<LDConnector>(new LDConnector());
            inConnector->InstanceName = "IN_" + QUuid::createUuid().toString().remove("{").remove("}").remove("-");
            inConnector->PinId = newPinId;
            inConnector->NewAdd = false;
            inConnector->Name = "IN" + QString::number(newPinId);
            inConnector->Direction = "Input";

            // 判断元件类型是否为Variable
            if (com_ptr->Type == "Variable")
            {
                inConnector->DataType = "Variable";
                inConnector->Visible = false; // 变量元件引脚不显示
            }
            else
            {
                inConnector->DataType = "BOOL";
            }

            // 添加引脚到元件
            com_ptr->connectorList.append(inConnector);

            // 更新元件的输入引脚数量
            com_ptr->InputPinNum++;

            // 重新计算引脚位置
            com_ptr->setPinPosOffset();
            // 对连接进行更新，调整受影响连接的引脚ID
            for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
            {
                if (connection->TargetComponentNumber == compNumber && connection->TargetPinId >= newPinId)
                {
                    connection->TargetPinId++; // 使ID更正
                }
            }

            // 同步更新相关变量元件的ParentPinId
            for (auto &component : ld->components->componentMap)
            {
                if (component->Type == "Variable" && component->ParentNumber == compNumber &&
                    component->ParentPinId >= newPinId)
                {
                    component->ParentPinId++; // 使ParentPinId与调整后的引脚ID保持一致
                    LOG_INFO_DEFAULT("更新变量元件{}的ParentPinId: {} -> {}", component->Number,
                                     component->ParentPinId - 1, component->ParentPinId);
                }
            }
            return inConnector;
        }
        return nullptr;
    }
    return nullptr;
}

// 向指定元件的指定引脚位置下方插入OUT引脚
QSharedPointer<LDConnector> LDManager::insertOUTConnector(const QString &fileKey, int compNumber, int pinId)
{
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        // 检查元件是否存在
        if (ld->components->componentMap.contains(compNumber))
        {
            QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);

            // 验证pinId是否是一个有效的输出引脚ID（负数）
            if (pinId >= 0)
            {
                LOG_ERROR_DEFAULT("插入OUT引脚失败: 无效的引脚ID, pinId={} (输出引脚ID必须是负数)", pinId);
                return nullptr; // 无效的引脚ID
            }

            // 找到需要插入的位置（pinId之后的位置）
            // 计算新引脚的ID，应该是pinId-1（更负）
            int newPinId = pinId - 1;

            // 更新所有ID小于(更负)newPinId的引脚ID（包括newPinId）
            // 例如，如果newPinId是-4，那么所有ID <= -4的引脚ID都需要-1
            for (QSharedPointer<LDConnector> &connector : com_ptr->connectorList)
            {
                if (connector->Direction == "Output" && connector->PinId <= newPinId)
                {
                    connector->PinId--; // 使ID更负
                    // 更新引脚名称以匹配新ID
                    if (connector->Name.startsWith("OUT"))
                    {
                        connector->Name = "OUT" + QString::number(qAbs(connector->PinId));
                    }
                }
            }

            // 创建新的输出引脚
            QSharedPointer<LDConnector> outConnector = QSharedPointer<LDConnector>(new LDConnector());
            outConnector->InstanceName = "OUT_" + QUuid::createUuid().toString().remove("{").remove("}").remove("-");
            outConnector->PinId = newPinId;
            outConnector->NewAdd = false;
            outConnector->Name = "OUT" + QString::number(qAbs(newPinId));
            outConnector->Direction = "Output";

            // 判断元件类型是否为Variable
            if (com_ptr->Type == "Variable")
            {
                outConnector->DataType = "Variable";
                outConnector->Visible = false; // 变量元件引脚不显示
            }
            else
            {
                outConnector->DataType = "BOOL";
            }

            // 添加引脚到元件
            com_ptr->connectorList.append(outConnector);

            // 更新元件的输出引脚数量
            com_ptr->OutputPinNum++;

            // 重新计算引脚位置
            com_ptr->setPinPosOffset();

            // 对连接进行更新，调整受影响连接的引脚ID
            for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
            {
                if (connection->SourceComponentNumber == compNumber && connection->SourcePinId <= newPinId)
                {
                    connection->SourcePinId--; // 使ID更负
                }
            }

            // 同步更新相关变量元件的ParentPinId
            for (auto &component : ld->components->componentMap)
            {
                if (component->Type == "Variable" && component->ParentNumber == compNumber &&
                    component->ParentPinId <= newPinId)
                {
                    component->ParentPinId--; // 使ParentPinId与调整后的引脚ID保持一致
                    LOG_INFO_DEFAULT("更新变量元件{}的ParentPinId: {} -> {}", component->Number,
                                     component->ParentPinId + 1, component->ParentPinId);
                }
            }

            return outConnector;
        }
        return nullptr;
    }
    return nullptr;
}

// 添加起始元件
int LDManager::putStartOnEN(const QString &fileKey, int networkNumber)
{
    LOG_INFO_DEFAULT("开始为网络{}添加START元件", networkNumber);

    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();

        if (ld == nullptr)
        {
            LOG_ERROR_DEFAULT("Error: LDFile pointer is null");
            return -1;
        }

        if (ld->networks->networkMap.contains(networkNumber))
        {
            QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);

            if (network.isNull())
            {
                LOG_ERROR_DEFAULT("Error: LDNetwork pointer is null");
                return -1;
            }

            // 检查网络中是否已有起始常量元件
            bool hasStartComponent = false;
            QSharedPointer<LDComponent> startCom = getStartConstant(fileKey, networkNumber);
            if (!startCom.isNull())
            {
                hasStartComponent = true;
                LOG_INFO_DEFAULT("找到已存在的START元件，编号: {}", startCom->Number);
            }
            else
            {
                LOG_INFO_DEFAULT("未找到网络{}的START元件", networkNumber);
            }

            // 如果没有起始常量元件，则添加一个
            if (!hasStartComponent)
            {
                // 因为是新增网络，所以网络中没有元件
                if (network->componentMap.size() != 0)
                {
                    LOG_ERROR_DEFAULT("LDNetwork already has components, but no start component. Creating one.");
                    LOG_INFO_DEFAULT("网络{}中现有元件列表:", networkNumber);
                    for (auto it = network->componentMap.begin(); it != network->componentMap.end(); ++it)
                    {
                        auto comp = it.value();
                        if (comp)
                        {
                            LOG_INFO_DEFAULT("  元件{}: Type={}, ChildType={}, AuxContent={}, AuxContent1={}",
                                             comp->Number, comp->Type.toStdString(), comp->ChildType.toStdString(),
                                             comp->AuxContent.toStdString(), comp->AuxContent1.toStdString());
                        }
                    }
                    return -1;
                }

                // 添加起始常量元件
                startCom = QSharedPointer<LDComponent>(new LDComponent());

                int newComponentNumber = ld->getMaxComonpentNumber() + 1;
                LOG_INFO_DEFAULT("分配新的START元件编号: {}", newComponentNumber);

                startCom->Name = "Start_Variable";
                startCom->InstanceName = "Start_BOOL";
                startCom->NetworkNumber = networkNumber;
                startCom->Enable = network->Enable;
                startCom->Number = newComponentNumber;
                startCom->TaskName = "";
                startCom->TaskOrderNumber = 0;
                startCom->Type = "Variable";
                startCom->ChildType = "Constant";
                startCom->Width = 5;
                startCom->Height = 3;
                startCom->MinWidth = 3;
                startCom->AuxContent = "???";
                startCom->AuxContent1 = "Start";
                startCom->DataType_Local = "";
                startCom->Source = "";
                startCom->InputPinNum = 0;
                startCom->OutputPinNum = 0;
                startCom->Comment = "";
                startCom->SupportsInPinType = "";
                startCom->SupportsOutPinType = "";
                // 启动元件始终在网络中位置为(0,0)
                startCom->XPos = 0;
                startCom->YPos = 0;
                startCom->Visible = false;
                startCom->LeftOrRight = 3;
                startCom->ParentNumber = 0;
                startCom->ParentPinId = 0;
                startCom->ParentPinDataType = "";

                // 先插入该块到componentMap中才能往块上添加引脚
                network->componentMap.insert(startCom->Number, startCom);
                startCom->network = network;

                // 加入所有元件库中
                ld->components->componentMap.insert(startCom->Number, startCom);
            }

            // 检查启动元件是否已有EN和ENO引脚
            if (startCom.isNull())
            {
                LOG_ERROR_DEFAULT("Error: Start component pointer is null");
                return -1;
            }

            bool hasEnEnoPin = false;
            bool hasOutPin = false;

            for (auto &connector : startCom->connectorList)
            {
                if (connector.isNull())
                {
                    LOG_ERROR_DEFAULT("Warning: Found null pin pointer, skipping");
                    continue;
                }

                if (connector->Name == "EN" || connector->Name == "ENO")
                {
                    hasEnEnoPin = true;
                }
                if (connector->Direction == "Output" && connector->Name != "ENO")
                {
                    hasOutPin = true;
                }
            }

            // 如果没有EN和ENO引脚，添加它们
            if (!hasEnEnoPin)
            {
                addENAndENOConnector(fileKey, startCom->Number);
            }

            // 如果没有OUT引脚，添加它
            if (!hasOutPin)
            {
                addOUTConnector(fileKey, startCom->Number);
            }
            return startCom->Number;
        }
        else
        {
            LOG_ERROR_DEFAULT("Error: LDNetwork does not exist: {}", networkNumber);
            return -1;
        }
    }
    else
    {
        LOG_ERROR_DEFAULT("Error: LDFile does not exist: {}" + fileKey.toStdString());
    }
    return -1;
}

// 获取起始常量元件
QSharedPointer<LDComponent> LDManager::getStartConstant(const QString &fileKey, int networkNumber)
{
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();

        for (QSharedPointer<LDComponent> &component : ld->components->componentMap)
        {
            // Skip null components
            if (component.isNull())
            {
                LOG_ERROR_DEFAULT("Found null component, skipping");
                continue;
            }
            if (component->Type == "Variable" && component->ChildType == "Constant" && component->AuxContent == "???" &&
                component->AuxContent1 == "Start" && component->NetworkNumber == networkNumber)
            {
                return component;
            }
        }
        LOG_INFO_DEFAULT("未找到网络{}的START元件", networkNumber);
    }
    else
    {
        LOG_ERROR_DEFAULT("LDFile not found: {}", fileKey.toStdString());
    }
    return nullptr;
}

// 将某个块的EN引脚与起始常量元件的ENO引脚连接
bool LDManager::connectENToStart(const QString &fileKey, int compNumber)
{
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        QSharedPointer<LDComponent> startCom = getStartConstant(fileKey);
        if (ld->components->componentMap.contains(compNumber))
        {
            QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
            // 将起始常量元件的ENO和块的EN引脚连接
            QSharedPointer<LDConnection> newConnection =
                addConnection(fileKey, startCom->Number, -1, -1, compNumber, 1, -1, true);
            if (nullptr == newConnection)
            {
                LOG_ERROR_DEFAULT("起始常量元件ENO -> 元件EN连接失败");
                return false;
            }
            return true;
        }
    }
    return false;
}

// 获取某个块的第一个IN引脚
QSharedPointer<LDConnector> LDManager::getFirstINConnector(const QString &fileKey, int compNumber)
{
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        if (ld->components->componentMap.contains(compNumber))
        {
            QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
            for (QSharedPointer<LDConnector> &connector : com_ptr->connectorList)
            {
                if (connector->PinId == 2)
                {
                    return connector;
                }
            }
            // 如果没有找到IN引脚，则添加一个
            int inPinId = addINConnector(fileKey, compNumber);
            // 重新获取添加后的IN引脚
            for (QSharedPointer<LDConnector> &connector : com_ptr->connectorList)
            {
                if (connector->PinId == inPinId)
                {
                    return connector;
                }
            }
        }
    }
    return nullptr;
}

// 获取某个块的第一个OUT引脚/如果是用户自定义元件则返回ENO引脚
QSharedPointer<LDConnector> LDManager::getFirstOUTConnector(const QString &fileKey, int compNumber)
{
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        if (ld->components->componentMap.contains(compNumber))
        {
            QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
            for (QSharedPointer<LDConnector> &connector : com_ptr->connectorList)
            {
                if (connector->PinId == -2)
                {
                    return connector;
                }
            }
            // 如果没有找到OUT引脚，则添加一个
            int outPinId = addOUTConnector(fileKey, compNumber);
            // 重新获取添加后的OUT引脚
            for (QSharedPointer<LDConnector> &connector : com_ptr->connectorList)
            {
                if (connector->PinId == outPinId)
                {
                    return connector;
                }
            }
        }
    }
    LOG_ERROR_DEFAULT("获取某个块的第一个OUT引脚失败");
    return nullptr;
}

// 获取某个块的最后一个OUT引脚，如果找不到则添加一个
QSharedPointer<LDConnector> LDManager::getLastOUTConnector(const QString &fileKey, int compNumber)
{
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        if (ld->components->componentMap.contains(compNumber))
        {
            QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
            for (QSharedPointer<LDConnector> &connector : com_ptr->connectorList)
            {
                if (connector->Direction == "Output")
                {
                    return connector;
                }
            }

            // 如果没有找到OUT引脚，则添加一个
            int outPinId = addOUTConnector(fileKey, compNumber);
            if (outPinId == 0)
            {
                LOG_ERROR_DEFAULT("添加OUT引脚失败");
                return nullptr;
            }

            // 重新获取添加后的OUT引脚
            for (QSharedPointer<LDConnector> &connector : com_ptr->connectorList)
            {
                if (connector->PinId == outPinId)
                {
                    return connector;
                }
            }
        }
    }
    LOG_ERROR_DEFAULT("获取某个块的OUT引脚失败");
    return nullptr;
}

// 获取元件的第一个输出引脚
QSharedPointer<LDConnector> LDManager::getFirstOutputConnector(const QString &fileKey, int compNumber)
{
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        if (ld->components->componentMap.contains(compNumber))
        {
            QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
            for (QSharedPointer<LDConnector> &connector : com_ptr->connectorList)
            {
                if (connector->Direction == "Output" && connector->PinId == -1)
                {
                    return connector;
                }
            }
            return nullptr;
        }
    }
    LOG_ERROR_DEFAULT("获取元件的第一个输出引脚失败");
    return nullptr;
}

// 构造功能块(And/Or/Coil/Jump/Return)
QSharedPointer<LDComponent> LDManager::createBlock(const QString &fileKey, QSharedPointer<LDComponent> parentComponent,
                                                   const QString &blockType)
{
    if (!LDFileList.contains(fileKey))
    {
        return nullptr;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    QSharedPointer<LDComponent> blockCom = QSharedPointer<LDComponent>(new LDComponent());
    QString strUUID = QUuid::createUuid().toString().remove("{").remove("}").remove("-");
    blockCom->Name = "Variable_" + strUUID.left(5);
    blockCom->InstanceName = strUUID.left(5);
    blockCom->NetworkNumber = parentComponent->NetworkNumber;
    blockCom->Enable = parentComponent->Enable;
    blockCom->Number = ld->getMaxComonpentNumber() + 1;
    blockCom->TaskName = "";
    blockCom->TaskOrderNumber = 0;
    blockCom->Type = "Block";
    blockCom->ChildType = "Func";
    blockCom->AuxContent = blockType;
    blockCom->AuxContent1 = "";
    blockCom->DataType_Local = "";
    blockCom->Source = "";
    blockCom->InputPinNum = 0;
    blockCom->OutputPinNum = 0;
    blockCom->Comment = "";
    blockCom->SupportsInPinType = "";
    blockCom->SupportsOutPinType = "";
    blockCom->XPos = 0;
    blockCom->YPos = 0;
    blockCom->Height = 3;
    blockCom->Width = 5;

    // 设置特定类型的属性
    if (blockType == "Or")
    {
        blockCom->Visible = false;
        blockCom->LeftOrRight = 3;
    }
    else
    {
        blockCom->Visible = true;
        blockCom->LeftOrRight = 0;
    }

    // 插入该块到componentMap中
    ld->components->componentMap.insert(blockCom->Number, blockCom);
    blockCom->network = ld->networks->networkMap.value(blockCom->NetworkNumber);
    // 将块添加到所属的网络中
    ld->networks->networkMap.value(blockCom->NetworkNumber)->componentMap.insert(blockCom->Number, blockCom);

    // 添加EN和ENO引脚
    addENAndENOConnector(fileKey, blockCom->Number);

    return blockCom;
}

// 创建AND块
QSharedPointer<LDComponent> LDManager::createAndBlock(const QString &fileKey,
                                                      QSharedPointer<LDComponent> parentComponent)
{
    return createBlock(fileKey, parentComponent, "Conatct");
}

// 构造新变量元件绑定到指定元件
bool LDManager::connectVariableToComponent(const QString &fileKey, QSharedPointer<LDComponent> parentComponent)
{
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在");
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 构造变量块
    QSharedPointer<LDComponent> varCom = QSharedPointer<LDComponent>(new LDComponent());
    QString strUUID = QUuid::createUuid().toString().remove("{").remove("}").remove("-");
    varCom->Name = "Variable_" + strUUID.left(5);
    varCom->InstanceName = strUUID.left(5);
    varCom->NetworkNumber = parentComponent->NetworkNumber;
    varCom->Enable = parentComponent->Enable;
    varCom->Number = ld->getMaxComonpentNumber() + 1;
    varCom->TaskName = "";
    varCom->TaskOrderNumber = 0;
    varCom->Type = "Variable";
    varCom->ChildType = "Local";
    varCom->AuxContent = "???";
    varCom->AuxContent1 = "";
    varCom->DataType_Local = "";
    varCom->Source = "";
    varCom->InputPinNum = 0;
    varCom->OutputPinNum = 0;
    varCom->Comment = "";
    varCom->SupportsInPinType = "";
    varCom->SupportsOutPinType = "";
    varCom->XPos = 0;
    varCom->YPos = 0;
    varCom->Height = 3;
    varCom->Width = 5;
    varCom->Visible = true;
    varCom->LeftOrRight = 0;
    varCom->ParentNumber = parentComponent->Number;

    // 插入该元件到componentMap中
    ld->components->componentMap.insert(varCom->Number, varCom);
    varCom->network = ld->networks->networkMap.value(varCom->NetworkNumber);
    // 将变量块添加到所属的网络中
    ld->networks->networkMap.value(varCom->NetworkNumber)->componentMap.insert(varCom->Number, varCom);

    // parentComponent类型是为线圈/跳转/返回，则挂载parentComponent的输出引脚
    if (parentComponent->Type == "Block" && parentComponent->ChildType == "Func" &&
        (parentComponent->AuxContent == "Coil" || parentComponent->AuxContent == "Jump" ||
         parentComponent->AuxContent == "Return"))
    {
        // varCom添加IN引脚
        int inPinId = addINConnector(fileKey, varCom->Number);
        if (inPinId <= 0)
        {
            LOG_ERROR_DEFAULT("变量块添加IN引脚失败");
            return false;
        }

        QSharedPointer<LDConnector> outConnector = getFirstOUTConnector(fileKey, parentComponent->Number);
        if (!outConnector)
        {
            LOG_ERROR_DEFAULT("获取parentComponent的输出引脚失败");
            return false;
        }
        int outPinId = outConnector->PinId;
        // 建立连接将变量块添加到parentComponent的输出引脚
        int sourceConnectIndex = getMaxSourceConnectIndex(fileKey, parentComponent->Number) + 1;
        int targetConnectIndex = getMaxTargetConnectIndex(fileKey, varCom->Number) + 1;
        QSharedPointer<LDConnection> newConnection =
            addConnection(fileKey, parentComponent->Number, outPinId, sourceConnectIndex, varCom->Number, inPinId,
                          targetConnectIndex, true);
        if (nullptr == newConnection)
        {
            LOG_ERROR_DEFAULT("变量块OUT引脚 -> parentComponent{}的输出引脚连接失败", parentComponent->Number);
            return false;
        }
        // 设置变量块和parentComponent的父子关系
        varCom->ParentNumber = parentComponent->Number;
        varCom->ParentPinId = inPinId;
        varCom->ParentPinDataType = "BOOL";
    }
    // 挂载到parentComponent的输入引脚
    else
    {
        // varCom添加OUT引脚
        QSharedPointer<LDConnector> outConnector = getFirstOUTConnector(fileKey, varCom->Number);
        if (!outConnector)
        {
            LOG_ERROR_DEFAULT("变量块添加OUT引脚失败");
            return false;
        }
        int outPinId = outConnector->PinId;

        // 原元件添加IN引脚
        int inPinId = addINConnector(fileKey, parentComponent->Number);
        LOG_INFO_DEFAULT("元件{}添加IN引脚{}", parentComponent->Number, inPinId);

        // 建立连接将变量块添加到AND块左边
        int sourceConnectIndex = getMaxSourceConnectIndex(fileKey, varCom->Number) + 1;
        int targetConnectIndex = getMaxTargetConnectIndex(fileKey, parentComponent->Number) + 1;
        QSharedPointer<LDConnection> newConnection =
            addConnection(fileKey, varCom->Number, outPinId, sourceConnectIndex, parentComponent->Number, inPinId,
                          targetConnectIndex, true);
        if (nullptr == newConnection)
        {
            LOG_ERROR_DEFAULT("变量块OUT引脚 -> AND块IN引脚连接失败");
            return false;
        }
        // 设置变量块和AND块的父子关系
        varCom->ParentNumber = parentComponent->Number;
        varCom->ParentPinId = outPinId;
        varCom->ParentPinDataType = "BOOL";
    }
    return true;
}

// 构造新变量元件绑定到指定元件的指定输入或者输出引脚
bool LDManager::connectVariableToComponent(const QString &fileKey, QSharedPointer<LDComponent> parentComponent,
                                           int pinId)
{
    // 1. 输入参数验证
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: {}", fileKey.toStdString().c_str());
        return false;
    }

    if (!parentComponent)
    {
        LOG_ERROR_DEFAULT("父元件指针为空");
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();
    if (!ld)
    {
        LOG_ERROR_DEFAULT("LDFile指针为空");
        return false;
    }

    // 2. 验证指定引脚是否存在
    QSharedPointer<LDConnector> targetConnector = parentComponent->searchConnectorFromPinId(pinId);
    if (!targetConnector)
    {
        LOG_ERROR_DEFAULT("父元件{}中不存在引脚ID: {}", parentComponent->Number, pinId);
        return false;
    }

    // 3. 判断引脚类型和连接方向
    bool isInputPin = (pinId > 0); // 正数为输入引脚，负数为输出引脚

    LOG_INFO_DEFAULT("开始为元件{}的{}引脚{}创建变量元件", parentComponent->Number, isInputPin ? "输入" : "输出",
                     pinId);

    // 4. 创建变量元件
    QSharedPointer<LDComponent> varCom = QSharedPointer<LDComponent>(new LDComponent());
    QString strUUID = QUuid::createUuid().toString().remove("{").remove("}").remove("-");
    varCom->Name = "Variable_" + strUUID.left(5);
    varCom->InstanceName = strUUID.left(5);
    varCom->NetworkNumber = parentComponent->NetworkNumber;
    varCom->Enable = parentComponent->Enable;
    varCom->Number = ld->getMaxComonpentNumber() + 1;
    varCom->TaskName = "";
    varCom->TaskOrderNumber = 0;
    varCom->Type = "Variable";
    varCom->ChildType = "Local";
    varCom->AuxContent = "???";
    varCom->AuxContent1 = "";
    varCom->DataType_Local = "";
    varCom->Source = "";
    varCom->InputPinNum = 0;
    varCom->OutputPinNum = 0;
    varCom->Comment = "";
    varCom->SupportsInPinType = "";
    varCom->SupportsOutPinType = "";
    varCom->SupportsInPinAdd = false;
    varCom->SupportsOutPinAdd = false;
    varCom->Width = 3;
    varCom->Height = 3;
    varCom->MinWidth = 3;
    varCom->Visible = true;
    varCom->LeftOrRight = isInputPin ? 0 : 2; // 输入引脚时变量在左边(0)，输出引脚时在右边(2)
    varCom->ParentNumber = parentComponent->Number;
    varCom->ParentPinId = pinId;
    varCom->ParentPinDataType = targetConnector->DataType;
    varCom->BranchId = 0;

    // 6. 将变量元件添加到文件中
    ld->components->componentMap.insert(varCom->Number, varCom);

    // 找到对应网络并添加变量元件
    QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(parentComponent->NetworkNumber);
    if (network)
    {
        network->componentMap.insert(varCom->Number, varCom);
        varCom->network = network;
    }
    else
    {
        LOG_ERROR_DEFAULT("找不到网络: {}", parentComponent->NetworkNumber);
        return false;
    }

    // 7. 为变量元件添加相应引脚并建立连接
    if (isInputPin)
    {
        // 输入引脚情况：变量元件需要输出引脚连接到父元件的输入引脚
        int varOutPinId = addOUTConnector(fileKey, varCom->Number);
        if (varOutPinId == 0)
        {
            LOG_ERROR_DEFAULT("为变量元件{}添加输出引脚失败", varCom->Number);
            return false;
        }

        // 建立连接：变量元件输出引脚 -> 父元件输入引脚
        int sourceConnectIndex = getMaxSourceConnectIndex(fileKey, varCom->Number) + 1;
        int targetConnectIndex = getMaxTargetConnectIndex(fileKey, parentComponent->Number) + 1;
        QSharedPointer<LDConnection> newConnection =
            addConnection(fileKey, varCom->Number, varOutPinId, sourceConnectIndex, parentComponent->Number, pinId,
                          targetConnectIndex, true);
        if (nullptr == newConnection)
        {
            LOG_ERROR_DEFAULT("变量元件{}输出引脚 -> 父元件{}输入引脚{}连接失败", varCom->Number,
                              parentComponent->Number, pinId);
            return false;
        }

        LOG_INFO_DEFAULT("变量元件{}成功连接到父元件{}的输入引脚{}", varCom->Number, parentComponent->Number, pinId);
    }
    else
    {
        // 输出引脚情况：变量元件需要输入引脚接收父元件的输出引脚
        int varInPinId = addINConnector(fileKey, varCom->Number);
        if (varInPinId == 0)
        {
            LOG_ERROR_DEFAULT("为变量元件{}添加输入引脚失败", varCom->Number);
            return false;
        }

        // 建立连接：父元件输出引脚 -> 变量元件输入引脚
        int sourceConnectIndex = getMaxSourceConnectIndex(fileKey, parentComponent->Number) + 1;
        int targetConnectIndex = getMaxTargetConnectIndex(fileKey, varCom->Number) + 1;
        QSharedPointer<LDConnection> newConnection =
            addConnection(fileKey, parentComponent->Number, pinId, sourceConnectIndex, varCom->Number, varInPinId,
                          targetConnectIndex, true);
        if (nullptr == newConnection)
        {
            LOG_ERROR_DEFAULT("父元件{}输出引脚{} -> 变量元件{}输入引脚连接失败", parentComponent->Number, pinId,
                              varCom->Number);
            return false;
        }

        LOG_INFO_DEFAULT("父元件{}的输出引脚{}成功连接到变量元件{}", parentComponent->Number, pinId, varCom->Number);
    }

    return true;
}

// 创建OR块
QSharedPointer<LDComponent> LDManager::createOrBlock(const QString &fileKey, QSharedPointer<LDComponent> component)
{
    return createBlock(fileKey, component, "Or");
}

// 构造Coil块
QSharedPointer<LDComponent> LDManager::createCoilBlock(const QString &fileKey, QSharedPointer<LDComponent> component)
{
    QSharedPointer<LDComponent> coilBlock = createBlock(fileKey, component, "Coil");

    // 特殊处理：如果是从AND块创建的线圈，设置正确的坐标
    if (component->Type == "Block" && component->ChildType == "Func" && component->AuxContent == "Conatct")
    {
        coilBlock->XPos = component->XPos + 1;
        coilBlock->YPos = component->YPos;
        LOG_DEBUG_DEFAULT("线圈元件坐标直接设置为从AND块创建: X={}, Y={}", coilBlock->XPos, coilBlock->YPos);
    }

    return coilBlock;
}

// 根据输出引脚获取对应的连接
QSharedPointer<LDConnection> LDManager::getOutputConnectorLDConnection(const QString &fileKey, int compNumber,
                                                                       int pinId)
{
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: {}", fileKey.toStdString().c_str());
        return nullptr;
    }
    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();
    QSharedPointer<LDConnections> connections = ld->connections;
    for (QSharedPointer<LDConnection> &connection : connections->connectionList)
    {
        if (connection->SourceComponentNumber == compNumber && connection->SourcePinId == pinId)
        {
            return connection;
        }
    }
    return nullptr;
}

// 根据输入引脚获取对应的连接
QSharedPointer<LDConnection> LDManager::getInputConnectorLDConnection(const QString &fileKey, int compNumber, int pinId)
{
    if (!LDFileList.contains(fileKey))
    {
        return nullptr;
    }
    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();
    QSharedPointer<LDConnections> connections = ld->connections;
    for (QSharedPointer<LDConnection> &connection : connections->connectionList)
    {
        if (connection->TargetComponentNumber == compNumber && connection->TargetPinId == pinId)
        {
            return connection;
        }
    }
    return nullptr;
}

// 添加线圈 用户只能选择末尾位置插入线圈
int LDManager::addCoilComponent(const QString &fileKey, int networkNumber, int compNumber, const QString &AuxContent)
{
    std::string strAuxContent = AuxContent.toStdString();
    LOG_INFO_DEFAULT("开始添加线圈元件: networkNumber={}, compNumber={}, AuxContent={}", networkNumber, compNumber,
                     strAuxContent);

    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return -1;
    }
    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    if (!cFile)
    {
        LOG_ERROR_DEFAULT("文件指针为空: fileKey={}", fileKey.toStdString());
        return -1;
    }

    LDFile *ld = cFile.data();
    if (!ld)
    {
        LOG_ERROR_DEFAULT("文件原始指针为空: fileKey={}", fileKey.toStdString());
        return -1;
    }

    if (!ld->networks->networkMap.contains(networkNumber))
    {
        LOG_ERROR_DEFAULT("网络不存在: networkNumber={}", networkNumber);
        return -1;
    }
    QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);
    if (!network)
    {
        LOG_ERROR_DEFAULT("网络指针为空: networkNumber={}", networkNumber);
        return -1;
    }

    for (auto it = network->componentMap.begin(); it != network->componentMap.end(); ++it)
    {
        auto comp = it.value();
        if (comp)
        {
            LOG_INFO_DEFAULT("  元件{}: Type={}, ChildType={}, AuxContent={}", comp->Number, comp->Type.toStdString(),
                             comp->ChildType.toStdString(), comp->AuxContent.toStdString());
        }
    }

    if (!network->componentMap.contains(compNumber))
    {
        if (ld->components->componentMap.contains(compNumber))
        {
            auto globalComp = ld->components->componentMap.value(compNumber);
            if (globalComp)
            {
                LOG_INFO_DEFAULT("全局元件{}属性: Type={}, ChildType={}, AuxContent={}, NetworkNumber={}",
                                 globalComp->Number, globalComp->Type.toStdString(),
                                 globalComp->ChildType.toStdString(), globalComp->AuxContent.toStdString(),
                                 globalComp->NetworkNumber);
            }
        }
        if (-1 != compNumber)
        {
            return -1;
        }
    }
    // 获取水平插入位置元件
    QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
    if (!com_ptr)
    {
        LOG_ERROR_DEFAULT("元件指针为空: compNumber={}", compNumber);
        return -1;
    }
    QSharedPointer<LDComponent> coilComponent = nullptr;
    // 获取该元件所在Y轴的最后一个元件
    QSharedPointer<LDComponent> lastComponent = getLastComponent(fileKey, networkNumber, compNumber);
    if (!lastComponent)
    {
        LOG_ERROR_DEFAULT("获取最后一个元件失败: compNumber={}", compNumber);
        return -1;
    }
    LOG_DEBUG_DEFAULT("最后一个元件: lastComponent->Number={}", lastComponent->Number);
    // 如果目标元件是最后一个元件
    if (lastComponent->Number == compNumber)
    {
        //  如果目标元件是最后一个元件且是跳转/线圈/返回元件
        if (lastComponent && lastComponent->Type == "Block" && lastComponent->ChildType == "Func" &&
            (lastComponent->AuxContent == "Coil" || lastComponent->AuxContent == "Jump" ||
             lastComponent->AuxContent == "Return" || lastComponent->AuxContent == "Set1" ||
             lastComponent->AuxContent == "Set0"))
        {
            LOG_INFO_DEFAULT("目标元件是最后一个元件且是跳转/线圈/返回元件");
            // 找到与该线圈元件相连接的输出元件

            // 找到与该元件相连接的输出元件
            QSharedPointer<LDComponent> sourceComponent = findSerialPredecessor(ld, compNumber);
            if (sourceComponent)
            {
                // 输出元件获取第一个OUT引脚
                QSharedPointer<LDConnector> outConnector = getFirstOUTConnector(fileKey, sourceComponent->Number);
                if (!outConnector)
                {
                    LOG_ERROR_DEFAULT("插入输出引脚失败: sourceComponent->Number={}, inputConnection->SourcePinId={}",
                                      sourceComponent->Number, outConnector->PinId);
                    return -1;
                }
                int outPinId = outConnector->PinId;

                // 创建线圈/跳转/返回元件
                coilComponent = createBlock(fileKey, sourceComponent, AuxContent);
                if (!coilComponent)
                {
                    LOG_ERROR_DEFAULT("创建{}元件失败: sourceComponent->Number={}", strAuxContent,
                                      sourceComponent->Number);
                    return -1;
                }

                // 新线圈/跳转/返回元件添加IN引脚
                int inPinId = addINConnector(fileKey, coilComponent->Number);
                if (inPinId <= 0)
                {
                    LOG_ERROR_DEFAULT("为{}元件添加IN引脚失败: coilComponent->Number={}, inPinId={}", strAuxContent,
                                      coilComponent->Number, inPinId);
                    return -1;
                }
                int sourceConnectIndex = getMaxSourceConnectIndex(fileKey, sourceComponent->Number) + 1;
                int targetConnectIndex = getMaxTargetConnectIndex(fileKey, coilComponent->Number) + 1;
                // 建立连接将输出元件到线圈/跳转/返回元件
                QSharedPointer<LDConnection> newConnection =
                    addConnection(fileKey, sourceComponent->Number, outPinId, sourceConnectIndex, coilComponent->Number,
                                  inPinId, targetConnectIndex, true);
                LOG_INFO_DEFAULT("元件{}索引{}->元件{}索引{}", sourceComponent->Number, sourceConnectIndex,
                                 coilComponent->Number, targetConnectIndex);
                if (nullptr == newConnection)
                {
                    LOG_ERROR_DEFAULT("建立连接失败: sourceComponent->Number={}, outPinId={}, "
                                      "coilComponent->Number={}, inPinId={}",
                                      sourceComponent->Number, outPinId, coilComponent->Number, inPinId);
                    return -1;
                }
                // 构造变量块并连接到线圈/跳转/返回元件
                if (!connectVariableToComponent(fileKey, coilComponent))
                {
                    LOG_ERROR_DEFAULT("连接变量块到{}元件失败: coilComponent->Number={}", strAuxContent,
                                      coilComponent->Number);
                    return -1;
                }
            }
            else
            {
                LOG_ERROR_DEFAULT("源元件为空: SourceComponentNumber={}", sourceComponent->Number);
                return -1;
            }
        }
        else // 如果该元件是最后一个元件但不是跳转/线圈/返回元件
        {
            // 判断该元件是否有输出连接
            QSharedPointer<LDConnection> outputConnection = getOutputConnectorLDConnection(fileKey, compNumber, -2);
            if (outputConnection)
            {
                // 判断outputConnection->TargetComponentNumber有几个输入
                QSharedPointer<LDComponent> targetComponent =
                    ld->searchComponentFromNumber(outputConnection->TargetComponentNumber);
                LOG_INFO_DEFAULT("targetComponent->InputPinNum={}", targetComponent->InputPinNum);
                if ((outputConnection->TargetConnectIndex == 0 && outputConnection->SourceConnectIndex == 0) ||
                    (targetComponent->InputPinNum <= 2))
                {
                    LOG_INFO_DEFAULT("删除OR块");
                    deleteEndORBlock(fileKey, outputConnection->TargetComponentNumber);
                }
                else
                {
                    deleteOutputConnection(fileKey, compNumber);
                }
            }

            // 直接添加线圈元件
            coilComponent = createBlock(fileKey, com_ptr, AuxContent);
            if (!coilComponent)
            {
                LOG_ERROR_DEFAULT("创建{}元件失败: com_ptr->Number={}", strAuxContent, com_ptr->Number);
                return -1;
            }

            // 线圈/跳转/返回元件添加IN引脚
            int inPinId = addINConnector(fileKey, coilComponent->Number);
            if (inPinId <= 0)
            {
                LOG_ERROR_DEFAULT("为{}元件添加IN引脚失败: coilComponent->Number={}, inPinId={}", strAuxContent,
                                  coilComponent->Number, inPinId);
                return -1;
            }
            LOG_DEBUG_DEFAULT("目标元件: com_ptr->Number={}", com_ptr->Number);
            // 源元件新增或获取OUT引脚
            int outPinId = 0;
            outPinId = addOUTConnector(fileKey, com_ptr->Number);
            if (outPinId == 0)
            {
                LOG_ERROR_DEFAULT("为源元件添加OUT引脚失败: com_ptr->Number={}, outPinId={}", com_ptr->Number,
                                  outPinId);
                return -1;
            }
            int sourceConnectIndex = getMaxSourceConnectIndex(fileKey, com_ptr->Number) + 1;
            int targetConnectIndex = getMaxTargetConnectIndex(fileKey, coilComponent->Number) + 1;
            // 建立连接将目标元件到线圈/跳转/返回元件
            QSharedPointer<LDConnection> newConnection =
                addConnection(fileKey, com_ptr->Number, outPinId, sourceConnectIndex, coilComponent->Number, inPinId,
                              targetConnectIndex, true);
            if (nullptr == newConnection)
            {
                LOG_ERROR_DEFAULT("建立连接失败: com_ptr->Number={}, outPinId={}, coilComponent->Number={}, inPinId={}",
                                  com_ptr->Number, outPinId, coilComponent->Number, inPinId);
                return -1;
            }
            LOG_DEBUG_DEFAULT("建立连接成功: com_ptr->Number={}, sourceConnectIndex={}, coilComponent->Number={}, "
                              "targetConnectIndex={}",
                              com_ptr->Number, sourceConnectIndex, coilComponent->Number, targetConnectIndex);
            // 构造变量块并连接到线圈/跳转/返回元件
            if (!connectVariableToComponent(fileKey, coilComponent))
            {
                LOG_ERROR_DEFAULT("连接变量块到{}元件失败: coilComponent->Number={}", strAuxContent,
                                  coilComponent->Number);
                return -1;
            }
            LOG_DEBUG_DEFAULT("插入线圈元件成功: coilComponent->Number={}, 坐标({},{})", coilComponent->Number,
                              coilComponent->XPos, coilComponent->YPos);
        }
    }
    else // 如果插件位置元件不是最后一个元件,则插件位置元件为倒数第二个元件，最后一个元件为OR块
    {
        // 获取目标元件(AND块)的输出引脚
        QSharedPointer<LDConnector> outConnector = getLastOUTConnector(fileKey, com_ptr->Number);
        if (!outConnector)
        {
            LOG_ERROR_DEFAULT("获取输出连接器失败: compNumber={}", com_ptr->Number);
            return -1;
        }

        // 获取输出引脚的连接
        QSharedPointer<LDConnection> outConnection =
            getOutputConnectorLDConnection(fileKey, com_ptr->Number, outConnector->PinId);
        if (!outConnection)
        {
            LOG_ERROR_DEFAULT("获取输出连接失败: compNumber={}, PinId={}", com_ptr->Number, outConnector->PinId);
            return -1;
        }

        // 获取目标元件连接的下一个元件
        int targetCompNumber = outConnection->TargetComponentNumber;
        QSharedPointer<LDComponent> targetComponent = ld->searchComponentFromNumber(targetCompNumber);
        if (!targetComponent)
        {
            LOG_ERROR_DEFAULT("目标元件为空: TargetComponentNumber={}", targetCompNumber);
            return -1;
        }

        // 检查目标元件是否为OR元件
        if (targetComponent->Type == "Block" && targetComponent->ChildType == "Func" &&
            targetComponent->AuxContent == "Or")
        {
            // 删除行末尾OR块
            deleteEndORBlock(fileKey, targetComponent->Number);

            // 创建COIL/跳转/返回元件，在原OR元件位置
            coilComponent = createBlock(fileKey, com_ptr, AuxContent);
            if (!coilComponent)
            {
                LOG_ERROR_DEFAULT("创建{}元件失败: com_ptr->Number={}", strAuxContent, com_ptr->Number);
                return -1;
            }

            // 设置COIL/跳转/返回元件位置(和插入位置元件水平串联)
            coilComponent->XPos = com_ptr->XPos + 1;
            coilComponent->YPos = com_ptr->YPos;
            LOG_DEBUG_DEFAULT("设置{}元件坐标: X={}, Y={}", strAuxContent, coilComponent->XPos, coilComponent->YPos);

            // 为COIL/跳转/返回元件添加IN引脚
            int inPinId = addINConnector(fileKey, coilComponent->Number);
            if (inPinId <= 0)
            {
                LOG_ERROR_DEFAULT("为{}元件添加IN引脚失败: coilComponent->Number={}, inPinId={}", strAuxContent,
                                  coilComponent->Number, inPinId);
                return -1;
            }
            QSharedPointer<LDConnector> outputConnector = getFirstOUTConnector(fileKey, com_ptr->Number);
            // 建立AND块到COIL/跳转/返回元件的连接
            int sourceConnectIndex = getMaxSourceConnectIndex(fileKey, com_ptr->Number) + 1;
            int targetConnectIndex = getMaxTargetConnectIndex(fileKey, coilComponent->Number) + 1;
            QSharedPointer<LDConnection> newConnection =
                addConnection(fileKey, com_ptr->Number, outputConnector->PinId, sourceConnectIndex,
                              coilComponent->Number, inPinId, targetConnectIndex, true);
            if (nullptr == newConnection)
            {
                LOG_ERROR_DEFAULT("建立连接失败: 元件{}的sourceConnectIndex{}到{}的targetConnectIndex{}连接失败, ",
                                  com_ptr->Number, sourceConnectIndex, coilComponent->Number, targetConnectIndex);
                return -1;
            }
        }
        else
        {
            LOG_ERROR_DEFAULT("目标元件不是OR元件，无法转换为COIL元件: Type={}, ChildType={}, AuxContent={}",
                              targetComponent->Type.toStdString(), targetComponent->ChildType.toStdString(),
                              targetComponent->AuxContent.toStdString());
            return -1;
        }
    }
    return coilComponent->Number;
}

// 删除线圈
bool LDManager::deleteCoilComponent(const QString &fileKey, int networkNumber, int compNumber)
{
    if (!LDFileList.contains(fileKey))
    {
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 检查网络是否存在
    if (!ld->networks->networkMap.contains(networkNumber))
    {
        return false;
    }

    QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);

    // 检查线圈/跳转/返回元件是否存在
    if (!ld->components->componentMap.contains(compNumber))
    {
        return false; // 不是线圈/跳转/返回元件
    }

    QSharedPointer<LDComponent> coilComponent = ld->searchComponentFromNumber(compNumber);

    // 验证是否为线圈/跳转/返回元件
    if (coilComponent->Type != "Block" || coilComponent->ChildType != "Func" ||
        (coilComponent->AuxContent != "Coil" && coilComponent->AuxContent != "Jump" &&
         coilComponent->AuxContent != "Return"))
    {
        return false; // 不是线圈/跳转/返回元件
    }

    // 1. 找到线圈输入引脚及其连接的源元件
    QSharedPointer<LDConnector> inputConnector = nullptr;
    for (QSharedPointer<LDConnector> &connector : coilComponent->connectorList)
    {
        if (connector->Direction == "Input" && connector->PinId > 1) // EN的PinId=1，跳过EN引脚
        {
            inputConnector = connector;
            break;
        }
    }

    if (!inputConnector)
    {
        // 没有找到输入引脚，直接删除线圈元件
        network->componentMap.remove(compNumber);
        ld->components->componentMap.remove(compNumber);

        return true;
    }

    // 查找连接到输入引脚的连接
    QSharedPointer<LDConnection> inputConnection = nullptr;
    int connectionIndex = -1;
    for (int i = 0; i < ld->connections->connectionList.size(); i++)
    {
        QSharedPointer<LDConnection> &connection = ld->connections->connectionList[i];
        if (connection->TargetComponentNumber == compNumber && connection->TargetPinId == inputConnector->PinId)
        {
            inputConnection = connection;
            connectionIndex = i;
            break;
        }
    }

    if (!inputConnection)
    {
        // 没有找到输入连接，直接删除线圈元件
        network->componentMap.remove(compNumber);
        ld->components->componentMap.remove(compNumber);

        return true;
    }

    // 找到源元件
    int sourceCompNumber = inputConnection->SourceComponentNumber;
    int sourcePinId = inputConnection->SourcePinId;

    if (!ld->components->componentMap.contains(sourceCompNumber))
    {
        // 源元件不存在，直接删除线圈元件
        network->componentMap.remove(compNumber);
        ld->components->componentMap.remove(compNumber);

        return true;
    }

    QSharedPointer<LDComponent> sourceComponent = ld->searchComponentFromNumber(sourceCompNumber);

    // 2. 删除线圈元件及其连接

    // 从连接列表中移除
    ld->connections->connectionList.removeAt(connectionIndex);

    // 2.2 从网络元件映射中移除线圈
    network->componentMap.remove(compNumber);

    // 2.4 从元件映射中移除线圈
    ld->components->componentMap.remove(compNumber);

    // 3. 从源元件删除对应的输出引脚

    // 查找要删除的输出引脚
    QSharedPointer<LDConnector> outputConnectorToRemove = nullptr;
    for (QSharedPointer<LDConnector> &connector : sourceComponent->connectorList)
    {
        if (connector->Direction == "Output" && connector->PinId == sourcePinId)
        {
            outputConnectorToRemove = connector;
            break;
        }
    }

    componentDeleteConnector(fileKey, sourceCompNumber, sourcePinId);

    // 重新排序连接索引
    QList<int> affectedComponents;
    if (sourceComponent)
    {
        affectedComponents.append(sourceComponent->Number);
    }
    reorderConnectionIndexes(fileKey, affectedComponents);

    return true;
}

// 获取某个元件所在Y轴的最后一个元件
QSharedPointer<LDComponent> LDManager::getLastComponent(const QString &fileKey, int networkNumber, int compNumber)
{

    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return nullptr;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    if (!cFile)
    {
        LOG_ERROR_DEFAULT("文件指针为空: fileKey={}", fileKey.toStdString());
        return nullptr;
    }

    LDFile *ld = cFile.data();
    if (!ld)
    {
        LOG_ERROR_DEFAULT("文件原始指针为空: fileKey={}", fileKey.toStdString());
        return nullptr;
    }

    if (!ld->components->componentMap.contains(compNumber))
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return nullptr;
    }

    QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
    if (!com_ptr)
    {
        LOG_ERROR_DEFAULT("元件指针为空: compNumber={}", compNumber);
        return nullptr;
    }

    // 没有找到X坐标更大的元件，也会返回输入元件本身
    QSharedPointer<LDComponent> lastComponent = com_ptr;
    int maxXPos = com_ptr->XPos;

    int sameYCount = 0;
    for (auto it = ld->networks->networkMap.begin(); it != ld->networks->networkMap.end(); ++it)
    {
        QSharedPointer<LDNetwork> network = it.value();
        if (network->Number == networkNumber)
        {
            for (auto it = network->componentMap.begin(); it != network->componentMap.end(); ++it)
            {
                QSharedPointer<LDComponent> component = it.value();
                if (component->YPos == com_ptr->YPos)
                {
                    sameYCount++;
                    if (component->XPos > maxXPos)
                    {
                        lastComponent = component;
                        maxXPos = component->XPos;
                    }
                }
            }
        }
    }
    return lastComponent;
}

// 获取连接到指定元件输入的顶层OR节点的输出引脚(入口函数)
QSharedPointer<LDConnector> LDManager::getConnectedORNodeConnector(const QString &fileKey, int compNumber)
{
    if (!LDFileList.contains(fileKey))
    {
        return nullptr;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 检查元件是否存在
    if (!ld->components->componentMap.contains(compNumber))
    {
        return nullptr;
    }

    // 创建一个集合记录已经访问过的元件，防止循环追踪
    QSet<int> visitedComponents;
    visitedComponents.insert(compNumber);

    // 获取当前元件
    QSharedPointer<LDComponent> component = ld->searchComponentFromNumber(compNumber);

    // 用于存储找到的最上层OR节点的输出连接器
    QSharedPointer<LDConnector> topORConnector = nullptr;

    // 查找所有连接到该元件输入引脚的连接
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        if (connection->TargetComponentNumber == compNumber)
        {
            int sourceCompNumber = connection->SourceComponentNumber;

            // 检查源元件是否存在
            if (!ld->components->componentMap.contains(sourceCompNumber))
            {
                continue;
            }

            QSharedPointer<LDComponent> sourceComponent = ld->searchComponentFromNumber(sourceCompNumber);

            // 如果源元件是OR节点，记录其输出连接器
            if (sourceComponent->Type == "Block" && sourceComponent->ChildType == "Func" &&
                sourceComponent->AuxContent == "Or")
            {
                // 找到OR节点的相应输出连接器
                for (QSharedPointer<LDConnector> &connector : sourceComponent->connectorList)
                {
                    if (connector->PinId == connection->SourcePinId)
                    {
                        topORConnector = connector;
                        break;
                    }
                }
            }

            // 无论是否找到OR节点，都继续向上递归查找，寻找可能的更上层OR节点
            if (!visitedComponents.contains(sourceCompNumber))
            {
                visitedComponents.insert(sourceCompNumber);
                QSharedPointer<LDConnector> upperORConnector =
                    getConnectedORNodeConnectorRecursive(ld, sourceCompNumber, visitedComponents);

                // 如果找到了更上层的OR节点，更新topORConnector
                if (upperORConnector)
                {
                    topORConnector = upperORConnector;
                }
            }
        }
    }

    return topORConnector;
}

// 递归辅助函数：从给定元件向上追踪到OR节点
QSharedPointer<LDConnector> LDManager::getConnectedORNodeConnectorRecursive(LDFile *ld, int compNumber,
                                                                            QSet<int> &visitedComponents)
{
    // 获取当前元件
    QSharedPointer<LDComponent> component = ld->searchComponentFromNumber(compNumber);

    // 用于存储找到的最上层OR节点的输出连接器
    QSharedPointer<LDConnector> topORConnector = nullptr;

    // 查找所有连接到该元件输入引脚的连接
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        if (connection->TargetComponentNumber == compNumber)
        {
            int sourceCompNumber = connection->SourceComponentNumber;

            // 检查源元件是否存在
            if (!ld->components->componentMap.contains(sourceCompNumber))
            {
                continue;
            }

            QSharedPointer<LDComponent> sourceComponent = ld->searchComponentFromNumber(sourceCompNumber);

            // 如果源元件是OR节点，记录其输出连接器
            if (sourceComponent->Type == "Block" && sourceComponent->ChildType == "Func" &&
                sourceComponent->AuxContent == "Or")
            {
                // 找到OR节点的相应输出连接器
                for (QSharedPointer<LDConnector> &connector : sourceComponent->connectorList)
                {
                    if (connector->PinId == connection->SourcePinId)
                    {
                        topORConnector = connector;
                        break;
                    }
                }
            }

            // 无论是否找到OR节点，都继续向上递归查找，寻找可能的更上层OR节点
            if (!visitedComponents.contains(sourceCompNumber))
            {
                visitedComponents.insert(sourceCompNumber);
                QSharedPointer<LDConnector> upperORConnector =
                    getConnectedORNodeConnectorRecursive(ld, sourceCompNumber, visitedComponents);

                // 如果找到了更上层的OR节点，更新topORConnector
                if (upperORConnector)
                {
                    topORConnector = upperORConnector;
                }
            }
        }
    }

    return topORConnector;
}

// 判断两个元件是否连接
QSharedPointer<LDConnection> LDManager::getConnection(const QString &fileKey, int compNumber1, int compNumber2)
{
    // LOG_INFO_DEFAULT("compNumber1:{}, compNumber2:{}", compNumber1, compNumber2);
    if (!LDFileList.contains(fileKey))
    {
        return nullptr;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();
    // 检查元件是否存在
    if (!ld->components->componentMap.contains(compNumber1) || !ld->components->componentMap.contains(compNumber2))
    {
        return nullptr;
    }
    // 检查连接是否存在
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        if ((connection->SourceComponentNumber == compNumber1 && connection->TargetComponentNumber == compNumber2) ||
            (connection->SourceComponentNumber == compNumber2 && connection->TargetComponentNumber == compNumber1))
        {
            return connection;
        }
    }
    return nullptr;
}

// 查找水平串联的前驱元件
QSharedPointer<LDComponent> LDManager::findSerialPredecessor(LDFile *ld, int compNumber)
{
    // 检查元件是否存在
    if (!ld->components->componentMap.contains(compNumber))
    {
        return nullptr;
    }

    // 获取元件
    QSharedPointer<LDComponent> component = ld->searchComponentFromNumber(compNumber);

    // 检查元件是否存在
    if (!component)
    {
        return nullptr;
    }
    // 检查component的连接
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        if (connection->TargetComponentNumber == compNumber && connection->TargetConnectIndex == 0 &&
            connection->SourceConnectIndex == 0)
        {
            return ld->searchComponentFromNumber(connection->SourceComponentNumber);
        }
    }
    return nullptr;
}

// 查找水平串联的后继元件
QSharedPointer<LDComponent> LDManager::findSerialSuccessor(LDFile *ld, int compNumber)
{
    // 检查元件是否存在
    if (!ld->components->componentMap.contains(compNumber))
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return nullptr;
    }
    // 获取元件
    QSharedPointer<LDComponent> component = ld->searchComponentFromNumber(compNumber);

    // 检查元件是否存在
    if (!component)
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return nullptr;
    }
    // 检查component的连接
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        // 水平串联条件：SourceConnectIndex = 0 && TargetConnectIndex = 0  跳过变量元件
        if (connection->SourceComponentNumber == compNumber && connection->SourceConnectIndex == 0 &&
            connection->TargetConnectIndex == 0)
        {
            LOG_INFO_DEFAULT(
                "找到水平串联连接: 源元件{} (SourceConnectIndex: {}) -> 目标元件{} (TargetConnectIndex: {})",
                connection->SourceComponentNumber, connection->SourceConnectIndex, connection->TargetComponentNumber,
                connection->TargetConnectIndex);
            return ld->searchComponentFromNumber(connection->TargetComponentNumber);
        }
    }
    LOG_ERROR_DEFAULT("元件{}没有水平串联的后继元件", compNumber);
    return nullptr;
}

// 在传递给界面之前修复块元件链接
bool LDManager::fixBlockConnection(LDFile *ld, int networkNumber)
{
    QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);
    if (!network)
    {
        LOG_ERROR_DEFAULT("网络不存在: networkNumber={}", networkNumber);
        return false;
    }
    for (auto &component : network->componentMap)
    {
        // 判断元件是否是用户自定义元件
        if (!isUserDefinedComponent(ld, component))
        {
            continue;
        }
        // 删除EN和ENO引脚，将PinId = 2的引脚修改为EN引脚，将PinId = -2的引脚修改为ENO引脚
        for (auto &connector : component->connectorList)
        {
            if (connector->PinId == 1)
            {
                component->connectorList.removeOne(connector); // 删除EN引脚
                component->InputPinNum--;
            }
            else if (connector->PinId == -1)
            {
                component->connectorList.removeOne(connector); // 删除ENO引脚
                component->OutputPinNum--;
            }
            else if (connector->PinId == 2)
            {
                connector->Name = "EN";
            }
            else if (connector->PinId == -2)
            {
                connector->Name = "ENO";
            }
            else
            {
                // 在扩展块中connector->Name = "EN" 时 connector->PinId = 2
                if (connector->PinId > 1 && connector->Direction == "Input")
                {
                    connector->Name = "IN" + QString::number(connector->PinId - 1);
                }
                else
                {
                    connector->Name = "OUT" + QString::number(qAbs(-connector->PinId - 1));
                }
            }
        }
        // 重新分配剩余输入引脚的ID（从小到大）
        QList<QSharedPointer<LDConnector>> inputConnectors;

        // 收集剩余的输入引脚
        for (QSharedPointer<LDConnector> &connector : component->connectorList)
        {
            if (connector->Direction == "Input")
            {
                inputConnectors.append(connector);
            }
        }
        if (!inputConnectors.isEmpty())
        {
            // 按原始PinId排序（从小到大）
            std::sort(inputConnectors.begin(), inputConnectors.end(),
                      [](const QSharedPointer<LDConnector> &a, const QSharedPointer<LDConnector> &b) {
                          return a->PinId < b->PinId;
                      });

            // 创建旧ID到新ID的映射
            QMap<int, int> pinIdMap;
            bool pinIdChanged = false;
            int newPinId = 1;

            for (QSharedPointer<LDConnector> &connector : inputConnectors)
            {
                int oldPinId = connector->PinId;
                if (oldPinId != newPinId)
                {
                    pinIdMap[oldPinId] = newPinId;
                    connector->PinId = newPinId;
                    connector->Name = "IN" + QString::number(newPinId - 1);
                    pinIdChanged = true;
                    LOG_INFO_DEFAULT("输入引脚ID重新分配: {} -> {}", oldPinId, newPinId);
                }
                newPinId++;
            }

            // 更新所有使用这些引脚作为目标的连接
            if (!pinIdMap.isEmpty())
            {
                for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
                {
                    if (connection->TargetComponentNumber == component->Number &&
                        pinIdMap.contains(connection->TargetPinId))
                    {
                        connection->TargetPinId = pinIdMap[connection->TargetPinId];
                    }
                }
            }
        }
        // 收集剩余的输出引脚
        QList<QSharedPointer<LDConnector>> outputConnectors;
        for (QSharedPointer<LDConnector> &connector : component->connectorList)
        {
            if (connector->Direction == "Output")
            {
                outputConnectors.append(connector);
            }
        }
        if (!outputConnectors.isEmpty())
        {
            // 按原始PinId排序（从小到大）
            std::sort(outputConnectors.begin(), outputConnectors.end(),
                      [](const QSharedPointer<LDConnector> &a, const QSharedPointer<LDConnector> &b) {
                          return a->PinId < b->PinId;
                      });
            // 创建旧ID到新ID的映射
            QMap<int, int> pinIdMap;
            bool pinIdChanged = false;
            int newPinId = -1;
            for (QSharedPointer<LDConnector> &connector : outputConnectors)
            {
                int oldPinId = connector->PinId;
                if (oldPinId != newPinId)
                {
                    pinIdMap[oldPinId] = newPinId;
                    connector->PinId = newPinId;
                    connector->Name = "OUT" + QString::number(qAbs(-newPinId - 1));
                    pinIdChanged = true;
                    LOG_INFO_DEFAULT("输出引脚ID重新分配: {} -> {}", oldPinId, newPinId);
                }
                newPinId--;
            }
            if (!pinIdMap.isEmpty())
            {
                for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
                {
                    if (connection->SourceComponentNumber == component->Number &&
                        pinIdMap.contains(connection->SourcePinId))
                    {
                        connection->SourcePinId = pinIdMap[connection->SourcePinId];
                    }
                }
            }
        }
    }
    LOG_INFO_DEFAULT("修复块元件链接完成");

    return true;
}

// 根据界面传输数据恢复块元件链接
bool LDManager::restoreBlockConnection(LDFile *ld, int networkNumber)
{
    QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);
    if (!network)
    {
        LOG_ERROR_DEFAULT("网络不存在: networkNumber={}", networkNumber);
        return false;
    }
    for (auto &component : network->componentMap)
    {
        // 判断元件是否是用户自定义元件
        if (!isUserDefinedComponent(ld, component))
        {
            continue;
        }
        // 添加EN和ENO引脚
        for (auto &connector : component->connectorList)
        {
            if (connector->PinId == 2)
            {
                connector->Name = "EN";
            }
            else if (connector->PinId == -2)
            {
                connector->Name = "ENO";
            }
            else
            {
                if (connector->PinId > 1 && connector->Direction == "Input")
                {
                    connector->Name = "IN" + QString::number(connector->PinId);
                }
                else
                {
                    connector->Name = "OUT" + QString::number(qAbs(connector->PinId));
                }
            }
        }
        addENAndENOConnector(ld->Name, component->Number);
    }
    LOG_INFO_DEFAULT("恢复块元件链接");
    return true;
}

// 判断元件是否是用户自定义元件
bool LDManager::isUserDefinedComponent(LDFile *ld, QSharedPointer<LDComponent> com_ptr)
{
    if (com_ptr->Type == "Block" && com_ptr->AuxContent != "Conatct" && com_ptr->AuxContent != "Or" &&
        com_ptr->AuxContent != "Coil" && com_ptr->AuxContent != "Jump" && com_ptr->AuxContent != "Return" &&
        com_ptr->AuxContent != "Set1" && com_ptr->AuxContent != "Set0")
    {
        LOG_INFO_DEFAULT("检测到用户自定义元件: {}", com_ptr->Number);
        return true;
    }
    LOG_INFO_DEFAULT("检测到非用户自定义元件: {} Type:{}, ChildType:{}, AuxContent:{}", com_ptr->Number,
                     com_ptr->Type.toStdString(), com_ptr->ChildType.toStdString(), com_ptr->AuxContent.toStdString());
    return false;
}

// 查找水平路径上的第一个节点
QSharedPointer<LDComponent> LDManager::findFirstNodeInHorizontalPath(LDFile *ld, int compNumber)
{
    // 检查元件是否存在
    if (!ld->components->componentMap.contains(compNumber))
    {
        return nullptr;
    }
    // 检查component的连接，向前查找前驱元件
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        // 查找水平串联的前驱：当前元件作为目标，前驱元件作为源
        if (connection->TargetComponentNumber == compNumber && connection->TargetConnectIndex == 0 &&
            connection->SourceConnectIndex == 0)
        {
            QSharedPointer<LDComponent> predecessorComponent =
                ld->searchComponentFromNumber(connection->SourceComponentNumber);
            if (predecessorComponent && predecessorComponent->Type != "Variable")
            {
                LOG_INFO_DEFAULT("找到前驱元件: {} -> {}", connection->SourceComponentNumber, compNumber);
                // 递归查找更前面的元件
                return findFirstNodeInHorizontalPath(ld, connection->SourceComponentNumber);
            }
        }
    }
    // 如果当前元件没有水平串联的前驱，则返回当前元件（即为起始点）
    LOG_INFO_DEFAULT("找到分支起始点: {}", compNumber);
    return ld->searchComponentFromNumber(compNumber);
}

// 添加网络 beforeOrAfter = 0 在之前添加 / 1 在之后添加
bool LDManager::addNetwork(const QString &fileKey, int networkNumber, int beforeOrAfter)
{
    LOG_INFO_DEFAULT("{} {} {}", fileKey.toStdString(), networkNumber, beforeOrAfter);
    bool flag = false;
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();

        int newNetworkNumber = 0;

        // 检查是否有网络
        if (ld->networks->networkMap.isEmpty())
        {
            // 如果没有网络，直接添加网络0
            newNetworkNumber = 0;
        }
        else
        {
            // 如果有网络但指定的网络不存在，则直接返回false
            if (!ld->networks->networkMap.contains(networkNumber))
            {
                return false;
            }

            // 计算新网络的位置
            newNetworkNumber = (beforeOrAfter == 0) ? networkNumber : networkNumber + 1;

            // 调整编号：将newNetworkNumber及之后的网络编号都+1
            for (QSharedPointer<LDNetwork> &network : ld->networks->networkMap)
            {
                if (newNetworkNumber <= network->Number)
                {
                    network->Number = network->Number + 1;

                    for (QSharedPointer<LDComponent> &component : network->componentMap)
                    {
                        if (component->network != nullptr)
                        {
                            // 对应到新的NetworkNumber
                            component->NetworkNumber = component->network->Number;
                            // LOG_INFO_DEFAULT("{} {}", component->Number, component->network->Number);
                        }
                        else
                        {
                            LOG_INFO_DEFAULT("{} {}", component->Number, component->NetworkNumber);
                        }
                    }
                }
            }
        }

        // 重排页码序号
        ld->networks->sortNetworkList();

        // 插入新网络
        QSharedPointer<LDNetwork> newNetwork = QSharedPointer<LDNetwork>(new LDNetwork());
        newNetwork->Number = newNetworkNumber;
        newNetwork->Type = "";
        newNetwork->Enable = true;
        newNetwork->Label = "";
        ld->networks->networkMap.insert(newNetworkNumber, newNetwork);
        LOG_INFO_DEFAULT("新网络{}已创建并插入", newNetworkNumber);

        ld->reSize();
        flag = true;
        int startComponentNumber = putStartOnEN(fileKey, newNetworkNumber);
        LOG_INFO_DEFAULT("putStartOnEN返回的startComponentNumber: {}", startComponentNumber);

        if (startComponentNumber > 0)
        {
            LOG_INFO_DEFAULT("开始为START元件{}添加线圈", startComponentNumber);
            // 为新网络添加默认起始常量元件,在起始元件后添加线圈
            int coilNumber = addCoilComponent(fileKey, newNetworkNumber, startComponentNumber, "Coil");
            LOG_INFO_DEFAULT("addCoilComponent返回的线圈编号: {}", coilNumber);
        }
        else
        {
            LOG_ERROR_DEFAULT("START元件创建失败，无法添加线圈");
        }
    }
    return flag;
}

// 复制网络
QJsonObject LDManager::copyNetwork(const QString &fileKey, int networkNumber)
{
    LOG_INFO_DEFAULT("复制网络 networkNumber={}", networkNumber);

    // 检查文件是否存在
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return QJsonObject();
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 检查网络是否存在
    if (!ld->networks->networkMap.contains(networkNumber))
    {
        LOG_ERROR_DEFAULT("网络不存在: networkNumber={}", networkNumber);
        return QJsonObject();
    }

    QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);

    // 创建网络数据结构
    QJsonObject networkDataObj;

    // 1. 复制网络基本信息
    QJsonObject networkInfo = network->toJsonObject();
    networkDataObj["networkInfo"] = networkInfo;

    // 2. 复制网络中的所有元件
    QJsonArray componentsArray;
    for (const auto &component : network->componentMap)
    {
        QJsonObject componentObj = component->toJsonObject();
        componentsArray.append(componentObj);
    }
    networkDataObj["components"] = componentsArray;

    // 3. 复制网络内部的所有连接
    QJsonArray connectionsArray = collectNetworkConnections(fileKey, networkNumber);
    networkDataObj["connections"] = connectionsArray;

    // 4. 保存统计信息
    networkDataObj["originalNetworkNumber"] = networkNumber;
    networkDataObj["componentCount"] = componentsArray.size();
    networkDataObj["connectionCount"] = connectionsArray.size();

    LOG_INFO_DEFAULT("成功复制网络 {} 包含 {} 个元件和 {} 个连接", networkNumber, componentsArray.size(),
                     connectionsArray.size());
    LOG_INFO_DEFAULT("元件数据: {}",
                     QString(QJsonDocument(networkDataObj).toJson(QJsonDocument::Compact)).toStdString());
    return networkDataObj;
}

bool LDManager::addNetworkBefore(const QString &fileKey, int networkNumber)
{
    return addNetwork(fileKey, networkNumber, 0);
}

bool LDManager::addNetworkAfter(const QString &fileKey, int networkNumber)
{
    return addNetwork(fileKey, networkNumber, 1);
}

// 粘贴网络
bool LDManager::pasteNetwork(const QString &fileKey, int networkNumber, const QJsonObject &networkData,
                             int beforeOrAfter)
{
    LOG_INFO_DEFAULT("粘贴网络到网络 {} 的 {} 侧", networkNumber, beforeOrAfter == 0 ? "之前" : "之后");

    // 检查网络数据是否有效
    if (networkData.isEmpty())
    {
        LOG_ERROR_DEFAULT("网络数据为空，无法粘贴");
        return false;
    }

    // 检查文件是否存在
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    int newNetworkNumber = 0;

    // 检查是否有网络
    if (ld->networks->networkMap.isEmpty())
    {
        // 如果没有网络，直接添加网络0
        newNetworkNumber = 0;
    }
    else
    {
        // 如果有网络但指定的网络不存在，则直接返回false
        if (!ld->networks->networkMap.contains(networkNumber))
        {
            LOG_ERROR_DEFAULT("指定的网络不存在: networkNumber={}", networkNumber);
            return false;
        }

        // 计算新网络的位置（使用与addNetwork相同的逻辑）
        newNetworkNumber = (beforeOrAfter == 0) ? networkNumber : networkNumber + 1;

        // 调整编号：将newNetworkNumber及之后的网络编号都+1
        for (QSharedPointer<LDNetwork> &network : ld->networks->networkMap)
        {
            if (newNetworkNumber <= network->Number)
            {
                network->Number = network->Number + 1;

                for (QSharedPointer<LDComponent> &component : network->componentMap)
                {
                    if (component->network != nullptr)
                    {
                        // 对应到新的NetworkNumber
                        component->NetworkNumber = component->network->Number;
                    }
                    else
                    {
                        LOG_INFO_DEFAULT("元件 {} 的网络指针为空，NetworkNumber={}", component->Number,
                                         component->NetworkNumber);
                    }
                }
            }
        }
    }

    // 重排页码序号
    ld->networks->sortNetworkList();

    // 获取传入的网络数据
    QJsonObject networkInfo = networkData["networkInfo"].toObject();
    QJsonArray componentsArray = networkData["components"].toArray();
    QJsonArray connectionsArray = networkData["connections"].toArray();

    // 1. 创建新网络
    QSharedPointer<LDNetwork> newNetwork = QSharedPointer<LDNetwork>(new LDNetwork());
    newNetwork->Number = newNetworkNumber;
    newNetwork->Type = networkInfo["Type"].toString();
    newNetwork->Enable = networkInfo["Enable"].toBool();
    newNetwork->Label = networkInfo["Label"].toString();
    newNetwork->Comment = networkInfo["Comment"].toString();
    newNetwork->Height = networkInfo["Height"].toInt();

    // 将新网络添加到文件中
    ld->networks->networkMap.insert(newNetworkNumber, newNetwork);

    // 2. 重建网络中的所有元件
    QMap<int, int> componentNumberMap = rebuildNetworkComponents(fileKey, newNetworkNumber, componentsArray);
    if (componentNumberMap.isEmpty() && !componentsArray.isEmpty())
    {
        LOG_ERROR_DEFAULT("重建网络元件失败");
        // 清理已创建的网络
        ld->networks->networkMap.remove(newNetworkNumber);
        return false;
    }

    // 3. 重建网络内部的连接
    if (!rebuildNetworkConnections(fileKey, connectionsArray, componentNumberMap))
    {
        LOG_ERROR_DEFAULT("重建网络连接失败");
        // 清理已创建的网络和元件
        for (auto oldNumber : componentNumberMap.keys())
        {
            int newNumber = componentNumberMap[oldNumber];
            ld->components->componentMap.remove(newNumber);
        }
        ld->networks->networkMap.remove(newNetworkNumber);
        return false;
    }

    // 4. 建立网络与元件的关联关系
    ld->networkWithComponent();

    // 5. 调整文件大小
    ld->reSize();

    LOG_INFO_DEFAULT("成功粘贴网络 {} 包含 {} 个元件和 {} 个连接", newNetworkNumber, componentsArray.size(),
                     connectionsArray.size());
    return true;
}

// 剪切网络
QJsonObject LDManager::cutNetwork(const QString &fileKey, int networkNumber)
{
    LOG_INFO_DEFAULT("剪切网络 networkNumber={}", networkNumber);

    // 检查文件是否存在
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return QJsonObject();
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 检查网络是否存在
    if (!ld->networks->networkMap.contains(networkNumber))
    {
        LOG_ERROR_DEFAULT("网络不存在: networkNumber={}", networkNumber);
        return QJsonObject();
    }

    // 检查是否为最后一个网络（不允许剪切最后一个网络）
    if (ld->networks->networkMap.size() <= 1)
    {
        LOG_ERROR_DEFAULT("不允许剪切最后一个网络");
        return QJsonObject();
    }

    // 第一步：执行复制操作
    QJsonObject networkData = copyNetwork(fileKey, networkNumber);
    if (networkData.isEmpty())
    {
        LOG_ERROR_DEFAULT("复制网络失败，剪切操作中止");
        return QJsonObject();
    }

    LOG_INFO_DEFAULT("复制网络成功，开始删除操作");

    // 第二步：执行删除操作
    bool deleteSuccess = deleteNetwork(fileKey, networkNumber);
    if (deleteSuccess)
    {
        LOG_INFO_DEFAULT("成功剪切网络 {}", networkNumber);
        return networkData;
    }
    else
    {
        LOG_ERROR_DEFAULT("删除网络失败，剪切操作失败");
        return QJsonObject();
    }
}

// 清理无效连接
bool LDManager::cleanInvalidConnections(const QString &fileKey)
{
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件 {} 不存在", fileKey.toStdString());
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 收集涉及的元件编号，用于后续引脚清理
    QSet<int> affectedComponents;

    // 找出所有无效连接
    QList<int> invalidConnectionIndexes;
    for (int i = 0; i < ld->connections->connectionList.size(); i++)
    {
        QSharedPointer<LDConnection> &connection = ld->connections->connectionList[i];

        // 检查连接是否有效
        bool isValid = true;

        // 1. 源元件和目标元件不应为0
        if (connection->SourceComponentNumber == 0 || connection->TargetComponentNumber == 0)
        {
            isValid = false;
            LOG_INFO_DEFAULT("发现无效连接1: 源={}, 源PinId={}, 目标={}, 目标PinId={}",
                             connection->SourceComponentNumber, connection->SourcePinId,
                             connection->TargetComponentNumber, connection->TargetPinId);
        }
        // 2. 源元件和目标元件应该存在
        else if (!ld->components->componentMap.contains(connection->SourceComponentNumber) ||
                 !ld->components->componentMap.contains(connection->TargetComponentNumber))
        {
            isValid = false;
            LOG_INFO_DEFAULT("发现元件不存在的连接2: 源={}, 源PinId={}, 目标={}, 目标PinId={}",
                             connection->SourceComponentNumber, connection->SourcePinId,
                             connection->TargetComponentNumber, connection->TargetPinId);
        }
        // 3. PinId应该是有效的（不为0）
        else if (connection->SourcePinId == 0 || connection->TargetPinId == 0)
        {
            isValid = false;
            LOG_INFO_DEFAULT("发现引脚ID无效的连接3: 源={}, 源PinId={}, 目标={}, 目标PinId={}",
                             connection->SourceComponentNumber, connection->SourcePinId,
                             connection->TargetComponentNumber, connection->TargetPinId);
        }
        // 4. 检查引脚是否实际存在于元件中
        else
        {
            QSharedPointer<LDComponent> sourceComponent =
                ld->searchComponentFromNumber(connection->SourceComponentNumber);
            QSharedPointer<LDComponent> targetComponent =
                ld->searchComponentFromNumber(connection->TargetComponentNumber);

            bool sourcePinFound = false;
            bool targetPinFound = false;

            // 检查源引脚是否存在
            for (QSharedPointer<LDConnector> &connector : sourceComponent->connectorList)
            {
                if (connector->PinId == connection->SourcePinId)
                {
                    sourcePinFound = true;
                    break;
                }
            }

            // 检查目标引脚是否存在
            for (QSharedPointer<LDConnector> &connector : targetComponent->connectorList)
            {
                if (connector->PinId == connection->TargetPinId)
                {
                    targetPinFound = true;
                    break;
                }
            }

            if (!sourcePinFound || !targetPinFound)
            {
                isValid = false;
                LOG_INFO_DEFAULT("发现引脚不存在的连接4: 源={}, 源PinId={} ({}), 目标={}, 目标PinId={} ({})",
                                 connection->SourceComponentNumber, connection->SourcePinId,
                                 sourcePinFound ? "存在" : "不存在", connection->TargetComponentNumber,
                                 connection->TargetPinId, targetPinFound ? "存在" : "不存在");
            }
        }

        if (!isValid)
        {
            invalidConnectionIndexes.append(i);
            // 收集涉及的元件编号，用于后续引脚清理
            if (connection->SourceComponentNumber != 0)
            {
                affectedComponents.insert(connection->SourceComponentNumber);
            }
            if (connection->TargetComponentNumber != 0)
            {
                affectedComponents.insert(connection->TargetComponentNumber);
            }
        }
    }

    // 从后向前删除无效连接，以保持索引有效
    std::sort(invalidConnectionIndexes.begin(), invalidConnectionIndexes.end(), std::greater<int>());
    for (int index : invalidConnectionIndexes)
    {
        ld->connections->connectionList.removeAt(index);
        LOG_INFO_DEFAULT("删除了无效连接，索引={}", index);
    }

    // 清理涉及元件的未使用引脚
    if (!affectedComponents.isEmpty())
    {
        LOG_INFO_DEFAULT("开始清理 {} 个元件的未使用引脚", affectedComponents.size());
        for (int compNumber : affectedComponents)
        {
            // 检查元件是否仍然存在
            if (ld->components->componentMap.contains(compNumber))
            {
                LOG_INFO_DEFAULT("清理元件 {} 的未使用引脚", compNumber);
                // 清理未使用的输出引脚
                deleteExtraOutConnectors(fileKey, compNumber);
                // 清理未使用的输入引脚
                deleteExtraInConnectors(fileKey, compNumber);
            }
        }
        LOG_INFO_DEFAULT("完成引脚清理");
    }

    return !invalidConnectionIndexes.isEmpty(); // 返回是否删除了连接
}

bool LDManager::fixLDFileConnections(const QString &fileKey)
{
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件{}不存在", fileKey.toStdString());
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    bool hasFixed = true;
    if (!cleanInvalidConnections(fileKey))
    {
        hasFixed = false;
    }

    for (auto &network : ld->networks->networkMap)
    {
        if (!LDCoordinateFixer::instance().fixComponentCoordinates(ld, network->Number))
        {
            hasFixed = false;
        }
        if (!fixBlockConnection(ld, network->Number))
        {
            hasFixed = false;
        }
    }
    emit fileChanged(fileKey);
    return hasFixed;
}

bool LDManager::modifyConnectorNegated(const QString &fileKey, int compNumber, int pinId)
{
    bool flag = false;
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        if (ld->components->componentMap.contains(compNumber))
        {
            QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);
            // 找到引脚
            for (QSharedPointer<LDConnector> &conn_ptr : com_ptr->connectorList)
            {
                if (conn_ptr->PinId == pinId && conn_ptr->DataType == "BOOL")
                {
                    flag = true;
                    // 取反操作
                    conn_ptr->Negated = !conn_ptr->Negated;
                    break;
                }
            }
        }
    }
    return flag;
}

bool LDManager::modifyComponentComment(const QString &fileKey, int compNumber, const QString &comment)
{
    bool flag = false;
    if (LDFileList.contains(fileKey))
    {
        QSharedPointer<LDFile> cFile = LDFileList[fileKey];
        LDFile *ld = cFile.data();
        if (ld->components->componentMap.contains(compNumber))
        {
            QSharedPointer<LDComponent> com = ld->searchComponentFromNumber(compNumber);
            com->Comment = comment;
            flag = true;
        }
    }
    return flag;
}

// 置位:将线圈的AuxContent:Coil设置为AuxContent:Set1
bool LDManager::setCoilToSet1(const QString &fileKey, int compNumber)
{
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    if (!ld->components->componentMap.contains(compNumber))
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return false;
    }

    QSharedPointer<LDComponent> com = ld->searchComponentFromNumber(compNumber);
    com->AuxContent = "Set1";

    return true;
}

// 复位:将线圈的AuxContent:Coil设置为AuxContent:Set0
bool LDManager::resetCoilToSet0(const QString &fileKey, int compNumber)
{
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    if (!ld->components->componentMap.contains(compNumber))
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return false;
    }

    QSharedPointer<LDComponent> com = ld->searchComponentFromNumber(compNumber);
    com->AuxContent = "Set0";

    return true;
}

// 重置:将Set1/Set0的AuxContent设置为Coil
bool LDManager::resetToCoil(const QString &fileKey, int compNumber)
{
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    if (!ld->components->componentMap.contains(compNumber))
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return false;
    }

    QSharedPointer<LDComponent> com = ld->searchComponentFromNumber(compNumber);
    com->AuxContent = "Coil";

    return true;
}

// 添加引脚并绑定变量
bool LDManager::InsertConnectorAndVariable(const QString &fileKey, int compNumber, int pinId)
{
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();
    if (!ld)
    {
        LOG_ERROR_DEFAULT("LDFile指针为空");
        return false;
    }
    QSharedPointer<LDConnector> connector = nullptr;

    if (pinId > 0)
    {
        connector = insertINConnector(fileKey, compNumber, pinId);
    }
    else if (pinId < 0)
    {
        connector = insertOUTConnector(fileKey, compNumber, pinId);
    }
    else
    {
        LOG_ERROR_DEFAULT("引脚ID无效: pinId={}", pinId);
        return false;
    }
    QSharedPointer<LDComponent> com = ld->searchComponentFromNumber(compNumber);
    return connectVariableToComponent(fileKey, com, connector->PinId);
}

// 删除指定元件的指定引脚
bool LDManager::componentDeleteConnector(const QString &fileKey, int compNumber, int pinId)
{
    LOG_INFO_DEFAULT("删除引脚: compNumber={}, pinId={}", compNumber, pinId);

    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 检查元件是否存在
    if (!ld->components->componentMap.contains(compNumber))
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return false;
    }

    QSharedPointer<LDComponent> com_ptr = ld->searchComponentFromNumber(compNumber);

    // 查找要删除的连接器
    QSharedPointer<LDConnector> connectorToDelete = nullptr;
    for (QSharedPointer<LDConnector> &connector : com_ptr->connectorList)
    {
        if (connector->PinId == pinId)
        {
            connectorToDelete = connector;
            break;
        }
    }

    if (connectorToDelete == nullptr)
    {
        LOG_ERROR_DEFAULT("要删除的引脚不存在: compNumber={}, pinId={}", compNumber, pinId);
        return false;
    }

    // 检查该引脚是否有连接，如果有则先删除相关连接
    QList<QSharedPointer<LDConnection>> connectionsToRemove;
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        if ((connection->SourceComponentNumber == compNumber && connection->SourcePinId == pinId) ||
            (connection->TargetComponentNumber == compNumber && connection->TargetPinId == pinId))
        {
            connectionsToRemove.append(connection);
        }
    }

    // 删除相关连接
    for (QSharedPointer<LDConnection> &connection : connectionsToRemove)
    {
        ld->connections->connectionList.removeOne(connection);
        LOG_INFO_DEFAULT("删除相关连接: 源元件={}, 源引脚={}, 目标元件={}, 目标引脚={}",
                         connection->SourceComponentNumber, connection->SourcePinId, connection->TargetComponentNumber,
                         connection->TargetPinId);
    }

    // 根据引脚类型进行不同的处理
    if (connectorToDelete->Direction == "Input")
    {
        // 处理输入引脚删除（正数ID）
        if (pinId <= 0)
        {
            LOG_ERROR_DEFAULT("输入引脚ID应该是正数: pinId={}", pinId);
            return false;
        }

        // 更新所有ID大于被删除引脚ID的输入引脚ID（减1）
        for (QSharedPointer<LDConnector> &connector : com_ptr->connectorList)
        {
            if (connector->Direction == "Input" && connector->PinId > pinId)
            {
                connector->PinId--; // ID减1
                // 更新引脚名称以匹配新ID
                if (connector->Name.startsWith("IN"))
                {
                    connector->Name = "IN" + QString::number(connector->PinId);
                }
            }
        }

        // 更新相关连接的引脚ID
        for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
        {
            if (connection->TargetComponentNumber == compNumber && connection->TargetPinId > pinId)
            {
                connection->TargetPinId--; // ID减1
            }
        }

        // 更新元件的输入引脚数量
        com_ptr->InputPinNum--;
    }
    else if (connectorToDelete->Direction == "Output")
    {
        // 处理输出引脚删除（负数ID）
        if (pinId >= 0)
        {
            LOG_ERROR_DEFAULT("输出引脚ID应该是负数: pinId={}", pinId);
            return false;
        }

        // 更新所有ID小于被删除引脚ID的输出引脚ID（加1，使其更接近0）
        for (QSharedPointer<LDConnector> &connector : com_ptr->connectorList)
        {
            if (connector->Direction == "Output" && connector->PinId < pinId)
            {
                connector->PinId++; // ID加1（变得不那么负）
                // 更新引脚名称以匹配新ID
                if (connector->Name.startsWith("OUT"))
                {
                    connector->Name = "OUT" + QString::number(qAbs(connector->PinId));
                }
            }
        }

        // 更新相关连接的引脚ID
        for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
        {
            if (connection->SourceComponentNumber == compNumber && connection->SourcePinId < pinId)
            {
                connection->SourcePinId++; // ID加1（变得不那么负）
            }
        }

        // 更新元件的输出引脚数量
        com_ptr->OutputPinNum--;
    }
    else
    {
        LOG_ERROR_DEFAULT("未知的引脚方向: Direction={}", connectorToDelete->Direction.toStdString());
        return false;
    }

    // 从元件的连接器列表中删除该连接器
    com_ptr->connectorList.removeOne(connectorToDelete);

    // 重新计算引脚位置
    com_ptr->setPinPosOffset();

    LOG_INFO_DEFAULT("成功删除引脚: compNumber={}, pinId={}, Direction={}", compNumber, pinId,
                     connectorToDelete->Direction.toStdString());

    return true;
}

// 判断选中位置是否能添加线圈/跳转/返回元件 1.选中元件必须是其所在y轴的最后一个元件
bool LDManager::canAddCoilJumpReturn(const QString &fileKey, int networkNumber, int compNumber)
{
    // 获取ld文件
    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();
    // 获取元件
    QSharedPointer<LDComponent> comp = ld->searchComponentFromNumber(compNumber);
    if (nullptr == comp)
    {
        // LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return false;
    }
    // 获取y=0轴的最后一个OR块
    QSharedPointer<LDComponent> lastORBlock = getLastORBlock(ld, networkNumber);
    if (nullptr == lastORBlock)
    {
        // 判断选中位置是否是线圈如果是线圈可以添加
        if (comp->AuxContent == "Coil" || comp->AuxContent == "Jump" || comp->AuxContent == "Return")
        {
            return true;
        }
        else
        {
            // LOG_ERROR_DEFAULT("y=0轴的最后一个OR块不存在");
            return false;
        }
    }
    if (isParentOrGrandparent(ld, compNumber, lastORBlock->Number))
    {
        return true;
    }
    return false;
}

// 删除某个元件未被使用的输出引脚
void LDManager::deleteExtraOutConnectors(const QString &fileKey, int compNumber)
{
    // 检查文件是否存在
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 检查元件是否存在
    if (!ld->components->componentMap.contains(compNumber))
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return;
    }

    // 获取元件
    QSharedPointer<LDComponent> component = ld->searchComponentFromNumber(compNumber);
    int networkNumber = component->NetworkNumber;

    // 创建一个要删除的引脚ID列表
    QList<int> pinIdsToRemove;
    QList<QSharedPointer<LDConnector>> connectorToRemove;

    // 遍历元件的所有引脚
    for (QSharedPointer<LDConnector> &connector : component->connectorList)
    {
        // 只检查输出引脚 (PinId < 0)，并且不是ENO引脚 (PinId != -1)
        if (connector->PinId < 0 && connector->PinId != -1)
        {
            bool isUsed = false;

            // 检查该输出引脚是否存在作为源的连接
            for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
            {
                if (connection->SourceComponentNumber == compNumber && connection->SourcePinId == connector->PinId)
                {
                    isUsed = true;
                    break;
                }
            }

            // 如果该输出引脚没有被使用，添加到待删除列表
            if (!isUsed)
            {
                pinIdsToRemove.append(connector->PinId);
                connectorToRemove.append(connector);
            }
        }
    }

    // 如果有引脚需要删除
    if (!pinIdsToRemove.isEmpty())
    {
        // 删除引脚
        for (QSharedPointer<LDConnector> &connector : connectorToRemove)
        {
            component->connectorList.removeOne(connector);
            component->OutputPinNum--;
            LOG_INFO_DEFAULT("删除未使用的输出引脚: 元件={}, 引脚ID={}", compNumber, connector->PinId);
        }
        // 重新排列剩余引脚的位置
        component->setPinPosOffset();
    }
    // 重新给输出引脚id从大到小，重新从-2开始赋值
    bool pinIdChanged = false;
    // 收集所有非ENO的输出引脚（PinId < 0 且 PinId != -1）
    QList<QSharedPointer<LDConnector>> outputConnectors;
    for (QSharedPointer<LDConnector> &connector : component->connectorList)
    {
        if (connector->PinId < 0 && connector->PinId != -1)
        {
            outputConnectors.append(connector);
        }
    }

    // 如果有输出引脚，则重新赋值
    if (!outputConnectors.isEmpty())
    {
        // 按照名称排序输出引脚（可以根据需要调整排序方式）
        std::sort(outputConnectors.begin(), outputConnectors.end(),
                  [](const QSharedPointer<LDConnector> &a, const QSharedPointer<LDConnector> &b) {
                      return a->Name < b->Name;
                  });

        // 从-2开始，递减赋值这些引脚的PinId
        int newPinId = -2;
        QMap<int, int> pinIdMap; // 旧PinId -> 新PinId的映射

        for (QSharedPointer<LDConnector> &connector : outputConnectors)
        {
            int oldPinId = connector->PinId;
            if (oldPinId != newPinId) // 如果PinId已经变了，才需要更新
            {
                pinIdMap[oldPinId] = newPinId;
                connector->PinId = newPinId;
                connector->Name = "OUT" + QString::number(qAbs(newPinId));
                pinIdChanged = true;
                LOG_INFO_DEFAULT("重新赋值输出引脚ID: 元件={}, 旧ID={}, 新ID={}", compNumber, oldPinId, newPinId);
            }
            newPinId--; // 递减，为下一个引脚准备ID
        }

        // 更新所有使用这些引脚作为源的连接
        if (!pinIdMap.isEmpty())
        {
            for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
            {
                if (connection->SourceComponentNumber == compNumber && pinIdMap.contains(connection->SourcePinId))
                {
                    connection->SourcePinId = pinIdMap[connection->SourcePinId];
                }
            }
        }

        // 如果有引脚ID被修改，则重新发送元件更新通知
        if (pinIdChanged && pinIdsToRemove.isEmpty()) // 如果已经删除了引脚并发送了通知，则不需要再次发送
        {
            // 重新排列剩余引脚的位置
            component->setPinPosOffset();
        }
    }
}

// 删除某个元件未被使用的输入引脚，修改已使用的引脚id从小到大重新排序
void LDManager::deleteExtraInConnectors(const QString &fileKey, int compNumber)
{
    // 检查文件是否存在
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 检查元件是否存在
    if (!ld->components->componentMap.contains(compNumber))
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return;
    }

    QSharedPointer<LDComponent> component = ld->searchComponentFromNumber(compNumber);
    if (!component)
    {
        LOG_ERROR_DEFAULT("元件指针为空: compNumber={}", compNumber);
        return;
    }

    // 收集未使用的输入引脚
    QList<int> pinIdsToRemove;
    QList<QSharedPointer<LDConnector>> connectorToRemove;

    // 遍历元件的所有引脚
    for (QSharedPointer<LDConnector> &connector : component->connectorList)
    {
        // 只检查输入引脚 (PinId > 1)，跳过EN引脚 (PinId = 1)
        if (connector->PinId > 1 && connector->Direction == "Input")
        {
            bool isUsed = false;

            // 检查该输入引脚是否存在作为目标的连接
            for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
            {
                if (connection->TargetComponentNumber == compNumber && connection->TargetPinId == connector->PinId)
                {
                    isUsed = true;
                    break;
                }
            }

            // 如果该输入引脚没有被使用，添加到待删除列表
            if (!isUsed)
            {
                pinIdsToRemove.append(connector->PinId);
                connectorToRemove.append(connector);
            }
        }
    }

    // 删除未使用的引脚
    if (!pinIdsToRemove.isEmpty())
    {
        LOG_INFO_DEFAULT("元件{}删除未使用的输入引脚，数量: {}", compNumber, pinIdsToRemove.size());

        for (QSharedPointer<LDConnector> &connector : connectorToRemove)
        {
            component->connectorList.removeOne(connector);
            LOG_INFO_DEFAULT("删除输入引脚: PinId={}", connector->PinId);
        }

        // 更新元件的输入引脚数量
        component->InputPinNum -= pinIdsToRemove.size();

        // 重新计算引脚位置
        component->setPinPosOffset();
    }

    // 重新排序剩余输入引脚的ID
    reorderInputPinIds(fileKey, compNumber);
}

// 重新排序指定元件的输入引脚ID（从小到大），更新相关连接
void LDManager::reorderInputPinIds(const QString &fileKey, int compNumber)
{
    // 检查文件是否存在
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 检查元件是否存在
    if (!ld->components->componentMap.contains(compNumber))
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return;
    }

    QSharedPointer<LDComponent> component = ld->searchComponentFromNumber(compNumber);
    if (!component)
    {
        LOG_ERROR_DEFAULT("元件指针为空: compNumber={}", compNumber);
        return;
    }

    // 重新分配剩余输入引脚的ID（从小到大）
    QList<QSharedPointer<LDConnector>> inputConnectors;

    // 收集剩余的输入引脚（PinId > 1）
    for (QSharedPointer<LDConnector> &connector : component->connectorList)
    {
        if (connector->PinId > 1 && connector->Direction == "Input")
        {
            inputConnectors.append(connector);
        }
    }

    // 如果有输入引脚需要重新分配ID
    if (!inputConnectors.isEmpty())
    {
        // 按原始PinId排序（从小到大）
        std::sort(inputConnectors.begin(), inputConnectors.end(),
                  [](const QSharedPointer<LDConnector> &a, const QSharedPointer<LDConnector> &b) {
                      return a->PinId < b->PinId;
                  });

        // 创建旧ID到新ID的映射
        QMap<int, int> pinIdMap;
        bool pinIdChanged = false;
        int newPinId = 2; // 输入引脚从2开始递增

        for (QSharedPointer<LDConnector> &connector : inputConnectors)
        {
            int oldPinId = connector->PinId;
            if (oldPinId != newPinId)
            {
                pinIdMap[oldPinId] = newPinId;
                connector->PinId = newPinId;
                connector->Name = "IN" + QString::number(newPinId - 1);
                pinIdChanged = true;
                LOG_INFO_DEFAULT("输入引脚ID重新分配: {} -> {}", oldPinId, newPinId);
            }
            newPinId++;
        }

        // 更新所有使用这些引脚作为目标的连接
        if (!pinIdMap.isEmpty())
        {
            for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
            {
                if (connection->TargetComponentNumber == compNumber && pinIdMap.contains(connection->TargetPinId))
                {
                    connection->TargetPinId = pinIdMap[connection->TargetPinId];
                }
            }
        }

        // 如果引脚ID发生了变化，重新计算引脚位置
        if (pinIdChanged)
        {
            component->setPinPosOffset();
        }
    }
}

// 重新排序连接索引，确保SourceConnectIndex和TargetConnectIndex的连续性
void LDManager::reorderConnectionIndexes(const QString &fileKey, const QList<int> &affectedComponents)
{
    LOG_INFO_DEFAULT("开始重新排序连接索引");
    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    if (!ld)
    {
        LOG_ERROR_DEFAULT("无法获取LDFile对象");
        return;
    }

    // 遍历所有受影响的元件
    for (int compNumber : affectedComponents)
    {
        if (!ld->components->componentMap.contains(compNumber))
        {
            LOG_INFO_DEFAULT("元件不存在，跳过索引重排: compNumber={}", compNumber);
            continue;
        }

        QSharedPointer<LDComponent> component = ld->searchComponentFromNumber(compNumber);
        if (!component)
        {
            LOG_INFO_DEFAULT("无法获取元件，跳过索引重排: compNumber={}", compNumber);
            continue;
        }

        // 1. 重排该元件作为源的所有连接的SourceConnectIndex
        QList<QSharedPointer<LDConnection>> sourceConnections;
        for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
        {
            if (connection->SourceComponentNumber == compNumber)
            {
                sourceConnections.append(connection);
            }
        }

        // 按照原始SourceConnectIndex 从小到大排序
        std::sort(sourceConnections.begin(), sourceConnections.end(),
                  [](const QSharedPointer<LDConnection> &a, const QSharedPointer<LDConnection> &b) {
                      return a->SourceConnectIndex < b->SourceConnectIndex;
                  });

        // 重新分配SourceConnectIndex，确保从0开始连续
        LOG_INFO_DEFAULT("元件{}有{}个输出连接需要重排索引", compNumber, sourceConnections.size());
        for (int i = 0; i < sourceConnections.size(); ++i)
        {
            if (sourceConnections[i]->SourceConnectIndex != i)
            {
                // LOG_INFO_DEFAULT("调整连接SourceConnectIndex: 源元件={}, 目标元件={}, 源元件旧索引={},
                // 源元件新索引={}",
                //                  compNumber, sourceConnections[i]->TargetComponentNumber,
                //                  sourceConnections[i]->SourceConnectIndex, i);
                LOG_INFO_DEFAULT("调整连接SourceConnectIndex: 源元件{}旧索引{}->目标元件{}索引{}, "
                                 "源元件{}新索引{}->目标元件{}索引{}",
                                 compNumber, sourceConnections[i]->SourceConnectIndex,
                                 sourceConnections[i]->TargetComponentNumber, sourceConnections[i]->TargetConnectIndex,
                                 compNumber, i, sourceConnections[i]->TargetComponentNumber,
                                 sourceConnections[i]->TargetConnectIndex);
                sourceConnections[i]->SourceConnectIndex = i;
            }
        }

        // 2. 重排该元件作为目标的所有连接的TargetConnectIndex
        QList<QSharedPointer<LDConnection>> targetConnections;
        for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
        {
            if (connection->TargetComponentNumber == compNumber)
            {
                targetConnections.append(connection);
            }
        }

        // 按照原始TargetConnectIndex排序，保持连接的相对顺序
        std::sort(targetConnections.begin(), targetConnections.end(),
                  [](const QSharedPointer<LDConnection> &a, const QSharedPointer<LDConnection> &b) {
                      return a->TargetConnectIndex < b->TargetConnectIndex;
                  });

        // 重新分配TargetConnectIndex，确保从0开始连续
        LOG_INFO_DEFAULT("元件{}有{}个输入连接需要重排索引", compNumber, targetConnections.size());
        for (int i = 0; i < targetConnections.size(); ++i)
        {
            if (targetConnections[i]->TargetConnectIndex != i)
            {
                LOG_INFO_DEFAULT("调整连接TargetConnectIndex: 源元件{}旧索引{}->目标元件{}索引{}, "
                                 "源元件{}新索引{}->目标元件{}索引{}",
                                 targetConnections[i]->SourceComponentNumber, targetConnections[i]->SourceConnectIndex,
                                 compNumber, targetConnections[i]->TargetConnectIndex,
                                 targetConnections[i]->SourceComponentNumber, targetConnections[i]->SourceConnectIndex,
                                 compNumber, i);
                targetConnections[i]->TargetConnectIndex = i;
            }
        }
    }
    LOG_INFO_DEFAULT("连接索引重排完成");
}

// 调整源元件的输出连接索引
void LDManager::adjustExistingConnectionIndexes(const QString &fileKey, int sourceCompNumber, int newTargetCompNumber,
                                                int newSourceIndex, int newTargetIndex)
{
    LOG_INFO_DEFAULT("调整现有连接索引: 源元件{}的{}->新目标元件{}的{}", sourceCompNumber, newSourceIndex,
                     newTargetCompNumber, newTargetIndex);

    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    if (!ld)
    {
        LOG_ERROR_DEFAULT("无法获取LDFile对象");
        return;
    }

    // 调整源元件的输出连接索引
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        if (connection->SourceComponentNumber == sourceCompNumber &&
            connection->TargetComponentNumber != newTargetCompNumber)
        {
            // 如果现有连接的索引 >= 新连接的索引，则需要递增
            if (connection->SourceConnectIndex >= newSourceIndex)
            {
                LOG_INFO_DEFAULT("调整源连接索引: 目标元件={}, 旧索引={}, 新索引={}", connection->TargetComponentNumber,
                                 connection->SourceConnectIndex, connection->SourceConnectIndex + 1);
                connection->SourceConnectIndex++;
            }
        }
    }

    LOG_INFO_DEFAULT("现有连接索引调整完成");
}

// 调整OR块输入连接的索引
void LDManager::adjustORBlockInputIndexes(const QString &fileKey, int orBlockNumber, int insertIndex)
{
    LOG_INFO_DEFAULT("调整OR块输入连接索引: OR块={}, 插入索引={}", orBlockNumber, insertIndex);

    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    if (!ld)
    {
        LOG_ERROR_DEFAULT("无法获取LDFile对象");
        return;
    }

    // 调整OR块的所有输入连接索引
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        if (connection->TargetComponentNumber == orBlockNumber)
        {
            // 如果现有连接的索引 >= 插入索引，则需要递增
            if (connection->TargetConnectIndex >= insertIndex)
            {
                LOG_INFO_DEFAULT("调整OR块输入连接索引: 源元件={}, 旧索引={}, 新索引={}",
                                 connection->SourceComponentNumber, connection->TargetConnectIndex,
                                 connection->TargetConnectIndex + 1);
                connection->TargetConnectIndex++;
            }
        }
    }

    LOG_INFO_DEFAULT("OR块输入连接索引调整完成");
}

// 删除行末尾OR块 传入为OR块的Number
void LDManager::deleteEndORBlock(const QString &fileKey, int compNumber)
{
    // 获取ld文件
    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 获取元件
    QSharedPointer<LDComponent> orBlock = ld->searchComponentFromNumber(compNumber);
    // 删除该OR块
    ld->components->componentMap.remove(compNumber);
    orBlock->network->componentMap.remove(compNumber);
    //
    QSharedPointer<LDComponent> preComponent;
    QSharedPointer<LDComponent> nextComponent;
    // 删除以该OR块为目的元件的连接
    bool flag = false;
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        // 记录与OR块水平串联的源元件
        if (connection->TargetComponentNumber == compNumber && connection->TargetConnectIndex == 0 &&
            connection->SourceConnectIndex == 0)
        {
            preComponent = ld->searchComponentFromNumber(connection->SourceComponentNumber);
        }
        // 记录与OR块相连的目标元件
        if (connection->SourceComponentNumber == compNumber && connection->SourceConnectIndex == 0 &&
            connection->TargetConnectIndex == 0)
        {
            nextComponent = ld->searchComponentFromNumber(connection->TargetComponentNumber);
        }
        if (connection->TargetComponentNumber == compNumber || connection->SourceComponentNumber == compNumber)
        {
            ld->connections->connectionList.removeOne(connection);

            if (connection->TargetComponentNumber == compNumber)
            {
                deleteExtraOutConnectors(fileKey, connection->SourceComponentNumber);
            }
            else
            {
                deleteExtraOutConnectors(fileKey, connection->TargetComponentNumber);
            }
        }
    }
    if (preComponent && nextComponent)
    {
        nextComponent->XPos = preComponent->XPos + 1;
        QSharedPointer<LDConnector> outConnector = getFirstOUTConnector(fileKey, preComponent->Number);
        QSharedPointer<LDConnector> inConnector = getFirstINConnector(fileKey, nextComponent->Number);
        LOG_INFO_DEFAULT("源元件{}和目标元件{}建立连接", preComponent->Number, nextComponent->Number);
        int sourceConnectIndex = getMaxSourceConnectIndex(fileKey, preComponent->Number) + 1;
        int targetConnectIndex = getMaxTargetConnectIndex(fileKey, nextComponent->Number) + 1;
        QSharedPointer<LDConnection> newConnection =
            addConnection(fileKey, preComponent->Number, outConnector->PinId, sourceConnectIndex, nextComponent->Number,
                          inConnector->PinId, targetConnectIndex, true);
        if (nullptr == newConnection)
        {
            LOG_INFO_DEFAULT("源元件{}和目标元件{}建立连接失败", preComponent->Number, nextComponent->Number);
        }
        else
        {
            LOG_INFO_DEFAULT("源元件{}和目标元件{}建立连接成功", preComponent->Number, nextComponent->Number);
        }
    }
    else
    {
        LOG_INFO_DEFAULT("源元件或目标元件为空，无法建立连接");
    }

    // 重新排序连接索引
    QList<int> affectedComponents;
    if (preComponent)
    {
        affectedComponents.append(preComponent->Number);
    }
    if (nextComponent)
    {
        affectedComponents.append(nextComponent->Number);
    }
    reorderConnectionIndexes(fileKey, affectedComponents);
}

// 删除连接 某个元件作为输出元件的连接
void LDManager::deleteOutputConnection(const QString &fileKey, int compNumber)
{
    // 获取ld文件
    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 删除该元件的连接
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        if (connection->SourceComponentNumber == compNumber)
        {
            ld->connections->connectionList.removeOne(connection);
            // 删除目标元件的删除链接后多余的引脚
            deleteExtraOutConnectors(fileKey, connection->TargetComponentNumber);
        }
    }
}

// 查找与指定元件相关的变量元件
QList<QSharedPointer<LDComponent>> LDManager::findRelatedVariableComponents(const QString &fileKey, int compNumber)
{
    QList<QSharedPointer<LDComponent>> relatedVariables;

    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return relatedVariables;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 查找以当前元件为父元件的变量元件
    for (const auto &component : ld->components->componentMap)
    {
        if (component->Type == "Variable" && component->ChildType == "Local" && component->ParentNumber == compNumber)
        {
            relatedVariables.append(component);
            LOG_INFO_DEFAULT("找到元件{}挂载的变量元件{}", compNumber, component->Number);
        }
    }

    return relatedVariables;
}

// 收集网络内部的所有连接
QJsonArray LDManager::collectNetworkConnections(const QString &fileKey, int networkNumber)
{
    QJsonArray connectionsArray;

    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return connectionsArray;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 获取网络中的所有元件编号
    QSet<int> networkComponentNumbers;
    if (ld->networks->networkMap.contains(networkNumber))
    {
        QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);
        for (const auto &component : network->componentMap)
        {
            networkComponentNumbers.insert(component->Number);
        }
    }

    // 收集网络内部的连接（源元件和目标元件都在同一网络内）
    for (const auto &connection : ld->connections->connectionList)
    {
        if (networkComponentNumbers.contains(connection->SourceComponentNumber) &&
            networkComponentNumbers.contains(connection->TargetComponentNumber))
        {
            QJsonObject connectionObj = connection->toJsonObject();
            connectionsArray.append(connectionObj);
        }
    }

    LOG_INFO_DEFAULT("收集到网络 {} 内部的 {} 个连接", networkNumber, connectionsArray.size());
    return connectionsArray;
}

// 从JSON数据创建元件
QSharedPointer<LDComponent> LDManager::createComponentFromJson(const QJsonObject &componentJson, const QString &fileKey,
                                                               int networkNumber)
{
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return nullptr;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 创建新元件
    QSharedPointer<LDComponent> newComponent = QSharedPointer<LDComponent>(new LDComponent());

    // 生成新的UUID
    QString strUUID = QUuid::createUuid().toString().remove("{").remove("}").remove("-");

    // 设置元件基本属性
    newComponent->Name = componentJson["Name"].toString() + "_" + strUUID.left(5);
    newComponent->InstanceName = componentJson["InstanceName"].toString() + "_" + strUUID.left(5);
    newComponent->NetworkNumber = networkNumber;
    newComponent->Enable = componentJson["Enable"].toBool();
    newComponent->Number = ld->getMaxComonpentNumber() + 1;
    newComponent->TaskName = componentJson["TaskName"].toString();
    newComponent->TaskOrderNumber = ld->getMaxOrderNumbber(newComponent->TaskName) + 1;
    newComponent->Type = componentJson["Type"].toString();
    newComponent->ChildType = componentJson["ChildType"].toString();
    newComponent->AuxContent = componentJson["AuxContent"].toString();
    newComponent->AuxContent1 = componentJson["AuxContent1"].toString();
    newComponent->DataType_Local = componentJson["DataType_Local"].toString();
    newComponent->Source = componentJson["Source"].toString();
    newComponent->InputPinNum = componentJson["InputPinNum"].toInt();
    newComponent->OutputPinNum = componentJson["OutputPinNum"].toInt();
    newComponent->Comment = componentJson["Comment"].toString();
    newComponent->SupportsInPinType = componentJson["SupportsInPinType"].toString();
    newComponent->SupportsOutPinType = componentJson["SupportsOutPinType"].toString();
    newComponent->SupportsInPinAdd = componentJson["SupportsInPinAdd"].toBool();
    newComponent->SupportsOutPinAdd = componentJson["SupportsOutPinAdd"].toBool();
    newComponent->XPos = 0; // 位置将在粘贴时重新计算
    newComponent->YPos = 0;
    newComponent->Width = componentJson["Width"].toInt();
    newComponent->Height = componentJson["Height"].toInt();
    newComponent->MinWidth = componentJson["MinWidth"].toInt();
    newComponent->Visible = componentJson["Visible"].toBool();
    newComponent->LeftOrRight = componentJson["LeftOrRight"].toInt();
    newComponent->ParentNumber = componentJson["ParentNumber"].toInt();
    newComponent->ParentPinId = componentJson["ParentPinId"].toInt();
    newComponent->ParentPinDataType = componentJson["ParentPinDataType"].toString();

    // 创建引脚
    QJsonArray connectorsArray = componentJson["connectors"].toArray();
    for (const auto &connectorValue : connectorsArray)
    {
        QJsonObject connectorJson = connectorValue.toObject();
        QSharedPointer<LDConnector> connector = QSharedPointer<LDConnector>(new LDConnector());

        connector->InstanceName = connectorJson["InstanceName"].toString() + "_" + strUUID.left(5);
        connector->PinId = connectorJson["PinId"].toInt(); // 将在reassignPinIds中重新分配
        connector->NewAdd = connectorJson["NewAdd"].toBool();
        connector->Name = connectorJson["Name"].toString();
        connector->DataType = connectorJson["DataType"].toString();
        connector->SupportChangeDataType = connectorJson["SupportChangeDataType"].toBool();
        connector->DataTypeGroup = connectorJson["DataTypeGroup"].toString();
        connector->Negated = connectorJson["Negated"].toBool();
        connector->Direction = connectorJson["Direction"].toString();
        connector->Comment = connectorJson["Comment"].toString();
        connector->FastSignal = connectorJson["FastSignal"].toBool();
        connector->InitValue = connectorJson["InitValue"].toString();
        connector->isInitVar = connectorJson["isInitVar"].toBool();
        connector->XPos = connectorJson["XPos"].toInt();
        connector->YPos = connectorJson["YPos"].toInt();
        connector->Visible = connectorJson["Visible"].toBool();
        connector->IsLogical = connectorJson["IsLogical"].toBool();
        connector->ChildNumber = connectorJson["ChildNumber"].toInt();

        newComponent->connectorList.append(connector);
    }

    // 重新分配引脚ID
    reassignPinIds(newComponent);

    LOG_INFO_DEFAULT("从JSON创建元件成功: Number={}, Type={}, Name={}", newComponent->Number,
                     newComponent->Type.toStdString(), newComponent->Name.toStdString());
    return newComponent;
}

// 重新分配引脚ID
void LDManager::reassignPinIds(QSharedPointer<LDComponent> component)
{
    if (!component)
    {
        return;
    }

    int inputPinId = 2;   // 输入引脚从2开始递增
    int outputPinId = -2; // 输出引脚从-2开始递减

    // 按原始顺序重新分配引脚ID
    for (auto &connector : component->connectorList)
    {
        if (connector->Direction == "Input")
        {
            connector->PinId = inputPinId++;
        }
        else if (connector->Direction == "Output")
        {
            connector->PinId = outputPinId--;
        }
        else if (connector->Direction == "EN")
        {
            connector->PinId = 1; // EN引脚固定为1
        }
        else if (connector->Direction == "ENO")
        {
            connector->PinId = -1; // ENO引脚固定为-1
        }
    }

    LOG_INFO_DEFAULT("重新分配元件 {} 的引脚ID完成", component->Number);
}

// 从JSON重建网络元件
QMap<int, int> LDManager::rebuildNetworkComponents(const QString &fileKey, int newNetworkNumber,
                                                   const QJsonArray &componentsArray)
{
    QMap<int, int> componentNumberMap; // 旧编号 -> 新编号的映射

    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return componentNumberMap;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    QSharedPointer<LDNetwork> newNetwork = ld->networks->networkMap.value(newNetworkNumber);
    if (!newNetwork)
    {
        LOG_ERROR_DEFAULT("新网络不存在: networkNumber={}", newNetworkNumber);
        return componentNumberMap;
    }

    // 重建所有元件
    for (const auto &componentValue : componentsArray)
    {
        QJsonObject componentJson = componentValue.toObject();
        int oldComponentNumber = componentJson["Number"].toInt();

        // 使用现有的 createComponentFromJson 函数创建元件
        QSharedPointer<LDComponent> newComponent = createComponentFromJson(componentJson, fileKey, newNetworkNumber);
        if (!newComponent)
        {
            LOG_ERROR_DEFAULT("创建元件失败: oldNumber={}", oldComponentNumber);
            continue;
        }

        // 记录编号映射关系
        componentNumberMap[oldComponentNumber] = newComponent->Number;

        // 将元件添加到文件和网络中
        ld->components->componentMap.insert(newComponent->Number, newComponent);
        newNetwork->componentMap.insert(newComponent->Number, newComponent);
        newComponent->network = newNetwork;

        LOG_INFO_DEFAULT("重建元件成功: 旧编号={}, 新编号={}", oldComponentNumber, newComponent->Number);
    }

    LOG_INFO_DEFAULT("重建网络 {} 的 {} 个元件完成", newNetworkNumber, componentNumberMap.size());
    return componentNumberMap;
}

// 重建网络连接
bool LDManager::rebuildNetworkConnections(const QString &fileKey, const QJsonArray &connectionsArray,
                                          const QMap<int, int> &componentNumberMap)
{
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    int successCount = 0;
    int failCount = 0;

    // 重建所有连接
    for (const auto &connectionValue : connectionsArray)
    {
        QJsonObject connectionJson = connectionValue.toObject();

        // 获取原始的元件编号
        int oldSourceComponentNumber = connectionJson["SourceComponentNumber"].toInt();
        int oldTargetComponentNumber = connectionJson["TargetComponentNumber"].toInt();

        // 查找新的元件编号
        if (!componentNumberMap.contains(oldSourceComponentNumber) ||
            !componentNumberMap.contains(oldTargetComponentNumber))
        {
            LOG_ERROR_DEFAULT("找不到元件编号映射: 源元件={}, 目标元件={}", oldSourceComponentNumber,
                              oldTargetComponentNumber);
            failCount++;
            continue;
        }

        int newSourceComponentNumber = componentNumberMap[oldSourceComponentNumber];
        int newTargetComponentNumber = componentNumberMap[oldTargetComponentNumber];

        // 创建新连接
        QSharedPointer<LDConnection> newConnection = QSharedPointer<LDConnection>(new LDConnection());
        newConnection->SourceComponentNumber = newSourceComponentNumber;
        newConnection->SourcePinId = connectionJson["SourcePinId"].toInt();
        newConnection->SourceConnectIndex = connectionJson["SourceConnectIndex"].toInt();
        newConnection->TargetComponentNumber = newTargetComponentNumber;
        newConnection->TargetPinId = connectionJson["TargetPinId"].toInt();
        newConnection->TargetConnectIndex = connectionJson["TargetConnectIndex"].toInt();
        // LDConnection 没有 Enable 属性，跳过这个设置
        // newConnection->Enable = connectionJson["Enable"].toBool();

        // 添加连接到文件中
        ld->connections->connectionList.append(newConnection);
        successCount++;

        LOG_INFO_DEFAULT("重建连接成功: {}:{} -> {}:{}", newSourceComponentNumber, newConnection->SourcePinId,
                         newTargetComponentNumber, newConnection->TargetPinId);
    }

    LOG_INFO_DEFAULT("重建连接完成: 成功={}, 失败={}", successCount, failCount);
    return failCount == 0;
}

// 获取下一个可用的网络编号
int LDManager::getNextAvailableNetworkNumber(const QString &fileKey, int afterNetworkNumber)
{
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return -1;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 如果没有网络，返回0
    if (ld->networks->networkMap.isEmpty())
    {
        return 0;
    }

    // 如果指定的网络不存在，使用最大网络编号
    if (!ld->networks->networkMap.contains(afterNetworkNumber))
    {
        int maxNumber = 0;
        for (const auto &network : ld->networks->networkMap)
        {
            if (network->Number > maxNumber)
            {
                maxNumber = network->Number;
            }
        }
        afterNetworkNumber = maxNumber;
    }

    // 寻找下一个可用的网络编号
    int candidateNumber = afterNetworkNumber + 1;
    while (ld->networks->networkMap.contains(candidateNumber))
    {
        candidateNumber++;
    }

    LOG_INFO_DEFAULT("获取到下一个可用的网络编号: {}", candidateNumber);
    return candidateNumber;
}

// 判断两个块是否有父子或祖孙关系
bool LDManager::isParentOrGrandparent(LDFile *ld, int sonNumber, int parentNumber)
{
    // 参数有效性检查
    if (!ld)
    {
        LOG_ERROR_DEFAULT("LDFile指针为空");
        return false;
    }

    if (sonNumber <= 0 || parentNumber <= 0)
    {
        LOG_ERROR_DEFAULT("无效的元件编号: sonNumber={}, parentNumber={}", sonNumber, parentNumber);
        return false;
    }

    // 如果两个编号相同，不存在父子关系
    if (sonNumber == parentNumber)
    {
        return false;
    }

    // 检查子元件是否存在
    if (!ld->components->componentMap.contains(sonNumber))
    {
        LOG_ERROR_DEFAULT("子元件不存在: sonNumber={}", sonNumber);
        return false;
    }

    // 检查父元件是否存在
    if (!ld->components->componentMap.contains(parentNumber))
    {
        LOG_ERROR_DEFAULT("父元件不存在: parentNumber={}", parentNumber);
        return false;
    }

    // 获取子元件
    QSharedPointer<LDComponent> currentComponent = ld->searchComponentFromNumber(sonNumber);
    if (!currentComponent)
    {
        LOG_ERROR_DEFAULT("获取子元件失败: sonNumber={}", sonNumber);
        return false;
    }

    // 使用集合记录已访问的元件，防止循环引用
    QSet<int> visitedComponents;
    visitedComponents.insert(sonNumber);

    // 沿着ParentNumber链向上追踪
    int currentParentNumber = currentComponent->ParentNumber;

    while (currentParentNumber > 0)
    {
        // 检查是否找到目标父元件
        if (currentParentNumber == parentNumber)
        {
            LOG_INFO_DEFAULT("找到父子/祖孙关系: {} 是 {} 的祖先", parentNumber, sonNumber);
            return true;
        }

        // 防止循环引用
        if (visitedComponents.contains(currentParentNumber))
        {
            LOG_ERROR_DEFAULT("检测到循环引用，停止追踪: currentParentNumber={}", currentParentNumber);
            break;
        }
        visitedComponents.insert(currentParentNumber);

        // 获取当前父元件
        if (!ld->components->componentMap.contains(currentParentNumber))
        {
            LOG_ERROR_DEFAULT("父元件不存在，停止追踪: currentParentNumber={}", currentParentNumber);
            break;
        }

        QSharedPointer<LDComponent> parentComponent = ld->searchComponentFromNumber(currentParentNumber);
        if (!parentComponent)
        {
            LOG_ERROR_DEFAULT("获取父元件失败，停止追踪: currentParentNumber={}", currentParentNumber);
            break;
        }

        // 继续向上追踪
        currentParentNumber = parentComponent->ParentNumber;
    }

    // 未找到父子/祖孙关系
    return false;
}

// 修改元件的编号
bool LDManager::modifyComponentNumber(const QString &fileKey, int compNumber, int newNumber)
{
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return false;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    if (!ld->components->componentMap.contains(compNumber))
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return false;
    }

    QSharedPointer<LDComponent> component = ld->searchComponentFromNumber(compNumber);
    if (!component)
    {
        LOG_ERROR_DEFAULT("元件不存在: compNumber={}", compNumber);
        return false;
    }

    int originalNewComNumber = component->Number;
    int targetNewComNumber = newNumber;

    QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(component->NetworkNumber);
    if (!network)
    {
        LOG_ERROR_DEFAULT("网络不存在: networkNumber={}", component->NetworkNumber);
        return false;
    }

    // 更新元件映射表：移除旧键值，添加新键值映射
    if (originalNewComNumber != targetNewComNumber)
    {
        // 首先更新元件对象本身的Number属性
        component->Number = targetNewComNumber;

        // 从网络映射表中更新
        if (network->componentMap.contains(originalNewComNumber))
        {
            network->componentMap.remove(originalNewComNumber);
            network->componentMap.insert(targetNewComNumber, component);
        }

        // 从全局元件映射表中更新
        if (ld->components->componentMap.contains(originalNewComNumber))
        {
            ld->components->componentMap.remove(originalNewComNumber);
            ld->components->componentMap.insert(targetNewComNumber, component);
        }

        // 更新所有连接中涉及该元件的编号引用
        for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
        {
            if (connection->SourceComponentNumber == originalNewComNumber)
            {
                connection->SourceComponentNumber = targetNewComNumber;
                LOG_INFO_DEFAULT("更新连接源元件编号: {} -> {}", originalNewComNumber, targetNewComNumber);
            }
            if (connection->TargetComponentNumber == originalNewComNumber)
            {
                connection->TargetComponentNumber = targetNewComNumber;
                LOG_INFO_DEFAULT("更新连接目标元件编号: {} -> {}", originalNewComNumber, targetNewComNumber);
            }
        }

        // 更新所有元件中引用该元件作为父元件的ParentNumber
        for (QSharedPointer<LDComponent> &component : ld->components->componentMap)
        {
            if (component->ParentNumber == originalNewComNumber)
            {
                component->ParentNumber = targetNewComNumber;
                LOG_INFO_DEFAULT("更新元件{}的父元件编号: {} -> {}", component->Number, originalNewComNumber,
                                 targetNewComNumber);
            }
        }

        LOG_INFO_DEFAULT("元件编号更新完成: {} -> {}", originalNewComNumber, targetNewComNumber);
    }
    return true;
}

// 从剪贴板数据创建元件
QSharedPointer<LDComponent> LDManager::createComponentFromClipboard(const QJsonObject &componentJson,
                                                                    const QString &fileKey, int networkNumber)
{
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return nullptr;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    // 获取新的元件编号
    int newNumber = ld->getMaxComonpentNumber() + 1;

    // 创建新元件
    QSharedPointer<LDComponent> newComponent = QSharedPointer<LDComponent>(new LDComponent());
    newComponent->Number = newNumber;
    newComponent->Type = componentJson["Type"].toString();
    newComponent->ChildType = componentJson["ChildType"].toString();
    newComponent->AuxContent = componentJson["AuxContent"].toString();
    newComponent->InstanceName = componentJson["InstanceName"].toString();
    newComponent->Name = componentJson["Name"].toString();
    newComponent->DataType_Local = componentJson["DataType_Local"].toString();
    newComponent->Source = componentJson["Source"].toString();
    newComponent->InputPinNum = componentJson["InputPinNum"].toInt();
    newComponent->OutputPinNum = componentJson["OutputPinNum"].toInt();
    newComponent->Comment = componentJson["Comment"].toString();
    newComponent->SupportsInPinType = componentJson["SupportsInPinType"].toString();
    newComponent->SupportsOutPinType = componentJson["SupportsOutPinType"].toString();
    newComponent->SupportsInPinAdd = componentJson["SupportsInPinAdd"].toBool();
    newComponent->SupportsOutPinAdd = componentJson["SupportsOutPinAdd"].toBool();
    newComponent->Width = componentJson["Width"].toInt();
    newComponent->Height = componentJson["Height"].toInt();
    newComponent->NetworkNumber = networkNumber;

    // 处理引脚信息（直接基于QJsonDocument引脚信息实现引脚添加，类似addCoilComponent）
    QJsonArray connectorsArray = componentJson["connectors"].toArray();
    for (const QJsonValue &connectorValue : connectorsArray)
    {
        QJsonObject connectorJson = connectorValue.toObject();
        QSharedPointer<LDConnector> connector = QSharedPointer<LDConnector>(new LDConnector());

        connector->PinId = connectorJson["PinId"].toInt();
        connector->Direction = connectorJson["Direction"].toString();
        connector->DataType = connectorJson["DataType"].toString();
        connector->Name = connectorJson["Name"].toString();
        connector->Comment = connectorJson["Comment"].toString();
        connector->InitValue = connectorJson["InitValue"].toString();
        connector->isInitVar = connectorJson["isInitVar"].toBool();
        connector->XPos = connectorJson["XPos"].toInt();
        connector->YPos = connectorJson["YPos"].toInt();
        connector->Visible = connectorJson["Visible"].toBool();
        connector->IsLogical = connectorJson["IsLogical"].toBool();
        connector->ChildNumber = connectorJson["ChildNumber"].toInt();

        newComponent->connectorList.append(connector);
    }

    // 重新分配引脚ID（基于原始顺序）
    if (newComponent->Type == "Block" &&
        (newComponent->ChildType == "FB" || newComponent->ChildType == "FUNC" || newComponent->ChildType == "ADVANCE"))
    {
        // 对于功能块，重新分配引脚ID
        int inputPinId = 2;
        int outputPinId = -2;

        for (auto &connector : newComponent->connectorList)
        {
            if (connector->Direction == "IN")
            {
                connector->PinId = inputPinId++;
            }
            else if (connector->Direction == "OUT")
            {
                connector->PinId = outputPinId--;
            }
        }
    }

    LOG_INFO_DEFAULT("从剪贴板创建元件成功: Number={}, Type={}, Name={}", newComponent->Number,
                     newComponent->Type.toStdString(), newComponent->Name.toStdString());
    return newComponent;
}

// 获取y=0轴的最后一个OR块
QSharedPointer<LDComponent> LDManager::getLastORBlock(LDFile *ld, int networkNumber)
{
    QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);
    if (!network)
    {
        LOG_ERROR_DEFAULT("网络指针为空: networkNumber={}", networkNumber);
        return nullptr;
    }

    // 查找y=0轴上的所有OR块
    QSharedPointer<LDComponent> lastORBlock = nullptr;
    int maxXPos = -1;

    for (auto it = network->componentMap.begin(); it != network->componentMap.end(); ++it)
    {
        QSharedPointer<LDComponent> component = it.value();

        // 检查是否为OR块且在y=0轴上
        if (component && component->Type == "Block" && component->ChildType == "Func" &&
            component->AuxContent == "Or" && component->YPos == 0)
        {
            // 找到X坐标最大的OR块
            if (component->XPos > maxXPos)
            {
                lastORBlock = component;
                maxXPos = component->XPos;
                // LOG_INFO_DEFAULT("找到y=0轴OR块: Number={}, XPos={}, YPos={}",
                // component->Number, component->XPos, component->YPos);
            }
        }
    }
    return lastORBlock;
}

QString LDManager::getUUID()
{
    return QUuid::createUuid().toString().remove("{").remove("}").remove("-");
}

// 获取某个块的最大SourceConnectIndex
int LDManager::getMaxSourceConnectIndex(const QString &fileKey, int compNumber)
{
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return -1;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    if (!ld)
    {
        LOG_ERROR_DEFAULT("无法获取LDFile对象");
        return -1;
    }

    // 检查元件是否存在
    if (!ld->components->componentMap.contains(compNumber))
    {
        LOG_ERROR_DEFAULT("元件{}不存在", compNumber);
        return -1;
    }

    int maxIndex = -1;
    // 遍历所有连接，找出指定元件作为源的最大连接索引
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        if (connection->SourceComponentNumber == compNumber)
        {
            if (connection->SourceConnectIndex > maxIndex)
            {
                maxIndex = connection->SourceConnectIndex;
            }
        }
    }
    LOG_INFO_DEFAULT("元件{}的最大SourceConnectIndex为:{}", compNumber, maxIndex);
    return maxIndex;
}

int LDManager::getMaxTargetConnectIndex(const QString &fileKey, int compNumber)
{
    if (!LDFileList.contains(fileKey))
    {
        LOG_ERROR_DEFAULT("文件不存在: fileKey={}", fileKey.toStdString());
        return -1;
    }

    QSharedPointer<LDFile> cFile = LDFileList[fileKey];
    LDFile *ld = cFile.data();

    if (!ld)
    {
        LOG_ERROR_DEFAULT("无法获取LDFile对象");
        return -1;
    }

    // 检查元件是否存在
    if (!ld->components->componentMap.contains(compNumber))
    {
        LOG_ERROR_DEFAULT("元件{}不存在", compNumber);
        return -1;
    }

    int maxIndex = -1;
    // 遍历所有连接，找出指定元件作为源的最大连接索引
    for (QSharedPointer<LDConnection> &connection : ld->connections->connectionList)
    {
        if (connection->TargetComponentNumber == compNumber)
        {
            if (connection->TargetConnectIndex > maxIndex)
            {
                maxIndex = connection->TargetConnectIndex;
            }
        }
    }
    LOG_INFO_DEFAULT("元件{}的最大TargetConnectIndex为:{}", compNumber, maxIndex);
    return maxIndex;
}
